'use client';

import React, { useState, useCallback } from 'react';
import { Copy, Check } from 'lucide-react';
import { cn } from '@/utils';

export interface MDXCodeInlineProps extends React.HTMLAttributes<HTMLElement> {
  /** 代码内容 */
  children: React.ReactNode;
  /** 自定义类名 */
  className?: string;
  /** 是否为行内代码 */
  inline?: boolean;
  /** 代码语言 */
  language?: string;
  /** 样式变体 */
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  /** 是否可复制 */
  copyable?: boolean;
}

// 样式变体映射
const variantStyles = {
  default: '',
  primary: 'text-primary bg-primary/10',
  secondary: 'text-secondary bg-secondary/10',
  success: 'text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-950',
  warning: 'text-amber-600 bg-amber-50 dark:text-amber-400 dark:bg-amber-950',
  error: 'text-red-600 bg-red-50 dark:text-red-400 dark:bg-red-950',
} as const;

/**
 * MDX 行内代码组件
 * - 支持多种样式变体
 * - 可复制功能
 * - 响应式设计
 * - 暗色模式支持
 * - 优化的性能和可访问性
 */
export const MDXCodeInline = React.forwardRef<HTMLElement, MDXCodeInlineProps>(
  ({ children, className, variant = 'default', copyable = false, ...props }, ref) => {
    const [copied, setCopied] = useState(false);

    // 处理内容，移除多余的引号
    const processContent = useCallback((content: React.ReactNode): React.ReactNode => {
      if (typeof content === 'string') {
        if (content.startsWith("'") && content.endsWith("'")) {
          return content.slice(1, -1);
        }
      }
      return content;
    }, []);

    // 复制文本处理
    const handleCopy = useCallback(async () => {
      if (typeof children === 'string') {
        try {
          await navigator.clipboard.writeText(children);
          setCopied(true);
          setTimeout(() => setCopied(false), 2000);
        } catch (err) {
          console.error('Failed to copy text:', err);
        }
      }
    }, [children]);

    return (
      <span className={cn('relative inline-block group', copyable && 'pr-6')}>
        <code
          ref={ref}
          className={cn(
            // 基础样式
            'relative inline-block rounded px-1.5 py-0.5',
            'font-mono text-sm font-medium',
            'border border-border/50',
            'transition-colors duration-200',
            // 默认背景
            'bg-muted/50',
            // 变体样式
            variantStyles[variant],
            // 隐藏反引号
            'before:content-[""] after:content-[""]',
            className
          )}
          {...props}
        >
          {processContent(children)}
        </code>
        {copyable && (
          <button
            type="button"
            onClick={handleCopy}
            className={cn(
              'absolute right-1 top-1/2 -translate-y-1/2',
              'opacity-0 group-hover:opacity-100',
              'hover:bg-accent hover:text-accent-foreground',
              'transition-all duration-200 p-1 rounded',
              'focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring'
            )}
            aria-label={copied ? '已复制' : '复制代码'}
          >
            {copied ? <Check className="h-3 w-3 text-green-600" /> : <Copy className="h-3 w-3" />}
          </button>
        )}
      </span>
    );
  }
);

MDXCodeInline.displayName = 'MDXCodeInline';

/**
 * 预设样式变体组件
 * 提供快速使用的样式化代码组件
 */
export const PrimaryCode = React.forwardRef<HTMLElement, Omit<MDXCodeInlineProps, 'variant'>>(
  (props, ref) => <MDXCodeInline ref={ref} variant="primary" {...props} />
);
PrimaryCode.displayName = 'PrimaryCode';

export const SecondaryCode = React.forwardRef<HTMLElement, Omit<MDXCodeInlineProps, 'variant'>>(
  (props, ref) => <MDXCodeInline ref={ref} variant="secondary" {...props} />
);
SecondaryCode.displayName = 'SecondaryCode';

export const SuccessCode = React.forwardRef<HTMLElement, Omit<MDXCodeInlineProps, 'variant'>>(
  (props, ref) => <MDXCodeInline ref={ref} variant="success" {...props} />
);
SuccessCode.displayName = 'SuccessCode';

export const WarningCode = React.forwardRef<HTMLElement, Omit<MDXCodeInlineProps, 'variant'>>(
  (props, ref) => <MDXCodeInline ref={ref} variant="warning" {...props} />
);
WarningCode.displayName = 'WarningCode';

export const ErrorCode = React.forwardRef<HTMLElement, Omit<MDXCodeInlineProps, 'variant'>>(
  (props, ref) => <MDXCodeInline ref={ref} variant="error" {...props} />
);
ErrorCode.displayName = 'ErrorCode';
