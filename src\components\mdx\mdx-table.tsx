'use client';

import React from 'react';
import { cn } from '@/utils';

// 表格组件属性接口
export interface MDXTableProps extends React.TableHTMLAttributes<HTMLTableElement> {
  children: React.ReactNode;
  className?: string;
}

export interface MDXTableHeadProps extends React.HTMLAttributes<HTMLTableSectionElement> {
  children: React.ReactNode;
  className?: string;
}

export interface MDXTableBodyProps extends React.HTMLAttributes<HTMLTableSectionElement> {
  children: React.ReactNode;
  className?: string;
}

export interface MDXTableRowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  children: React.ReactNode;
  className?: string;
}

export interface MDXTableHeaderProps extends React.ThHTMLAttributes<HTMLTableHeaderCellElement> {
  children: React.ReactNode;
  className?: string;
}

export interface MDXTableCellProps extends React.TdHTMLAttributes<HTMLTableDataCellElement> {
  children: React.ReactNode;
  className?: string;
}

/**
 * 表格样式配置
 */
const tableStyles = {
  table: cn(
    'w-full border-collapse',
    'my-6',
    'text-sm',
    'border border-border rounded-lg overflow-hidden'
  ),

  thead: cn('bg-muted/50'),

  tbody: cn('divide-y divide-border'),

  tr: cn('border-b border-border last:border-b-0', 'hover:bg-muted/30 transition-colors'),

  th: cn('px-4 py-3', 'text-left font-semibold', 'text-foreground', 'border-b border-border'),

  td: cn('px-4 py-3', 'text-muted-foreground', 'align-top'),
};

/**
 * MDX 表格组件
 */
export const MDXTable = React.forwardRef<HTMLTableElement, MDXTableProps>(
  ({ children, className, ...props }, ref) => (
    <div className="overflow-x-auto my-6">
      <table ref={ref} className={cn(tableStyles.table, className)} {...props}>
        {children}
      </table>
    </div>
  )
);

/**
 * 表格头部组件
 */
export const MDXTableHead = React.forwardRef<HTMLTableSectionElement, MDXTableHeadProps>(
  ({ children, className, ...props }, ref) => (
    <thead ref={ref} className={cn(tableStyles.thead, className)} {...props}>
      {children}
    </thead>
  )
);

/**
 * 表格主体组件
 */
export const MDXTableBody = React.forwardRef<HTMLTableSectionElement, MDXTableBodyProps>(
  ({ children, className, ...props }, ref) => (
    <tbody ref={ref} className={cn(tableStyles.tbody, className)} {...props}>
      {children}
    </tbody>
  )
);

/**
 * 表格行组件
 */
export const MDXTableRow = React.forwardRef<HTMLTableRowElement, MDXTableRowProps>(
  ({ children, className, ...props }, ref) => (
    <tr ref={ref} className={cn(tableStyles.tr, className)} {...props}>
      {children}
    </tr>
  )
);

/**
 * 表格头单元格组件
 */
export const MDXTableHeader = React.forwardRef<HTMLTableHeaderCellElement, MDXTableHeaderProps>(
  ({ children, className, ...props }, ref) => (
    <th ref={ref} className={cn(tableStyles.th, className)} {...props}>
      {children}
    </th>
  )
);

/**
 * 表格数据单元格组件
 */
export const MDXTableCell = React.forwardRef<HTMLTableDataCellElement, MDXTableCellProps>(
  ({ children, className, ...props }, ref) => (
    <td ref={ref} className={cn(tableStyles.td, className)} {...props}>
      {children}
    </td>
  )
);

// 设置显示名称
MDXTable.displayName = 'MDXTable';
MDXTableHead.displayName = 'MDXTableHead';
MDXTableBody.displayName = 'MDXTableBody';
MDXTableRow.displayName = 'MDXTableRow';
MDXTableHeader.displayName = 'MDXTableHeader';
MDXTableCell.displayName = 'MDXTableCell';

/**
 * 表格组件集合
 */
export const MDXTableComponents = {
  table: MDXTable,
  thead: MDXTableHead,
  tbody: MDXTableBody,
  tr: MDXTableRow,
  th: MDXTableHeader,
  td: MDXTableCell,
};

export default MDXTable;
