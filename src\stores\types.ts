/**
 * Zustand 状态管理类型定义
 * 统一管理所有状态相关的类型
 */

// ==================== 基础状态类型 ====================

/**
 * 应用主题状态
 */
export interface ThemeState {
  theme: 'light' | 'dark' | 'system';
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
}

/**
 * 用户认证状态
 */
export interface AuthState {
  isLoggedIn: boolean;
  loginTime: number | null;
  setLoginState: (isLoggedIn: boolean, loginTime?: number) => void;
  logout: () => void;
  checkLoginExpiry: () => boolean;
}

/**
 * 搜索状态
 */
export interface SearchState {
  isOpen: boolean;
  query: string;
  results: ZustandSearchResult[];
  isLoading: boolean;
  history: string[];
  selectedIndex: number;
  setOpen: (open: boolean) => void;
  setQuery: (query: string) => void;
  setResults: (results: ZustandSearchResult[]) => void;
  setLoading: (loading: boolean) => void;
  addToHistory: (query: string) => void;
  clearHistory: () => void;
  setSelectedIndex: (index: number) => void;
  resetSearch: () => void;
}

/**
 * Zustand 搜索结果类型
 */
export interface ZustandSearchResult {
  id: string;
  title: string;
  description?: string;
  url: string;
  type: 'blog' | 'docs' | 'tool' | 'link' | 'command';
  category?: string;
  tags?: string[];
  icon?: string;
}

/**
 * 导航栏状态
 */
export interface NavbarState {
  direction: 'up' | 'down';
  position: number;
  showTitle: boolean;
  pageTitle: string;
  lastDirectionChange: number;
  setScrollPosition: (position: number) => void;
  setPageTitle: (title: string) => void;
  scrollToTop: () => void;
}

/**
 * 工具状态
 */
export interface ToolState {
  input: string;
  output: string;
  loading: boolean;
  error: string | null;
  history: ToolHistoryItem[];
  setInput: (input: string) => void;
  setOutput: (output: string) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  addToHistory: (item: ToolHistoryItem) => void;
  clearHistory: () => void;
  reset: () => void;
}

/**
 * 工具历史记录项
 */
export interface ToolHistoryItem {
  id: string;
  timestamp: number;
  input: string;
  output: string;
  tool: string;
}

/**
 * MDX 组件状态
 */
export interface MDXState {
  components: Record<string, React.ComponentType<any>>;
  defaultProps: Record<string, any>;
  setComponents: (components: Record<string, React.ComponentType<any>>) => void;
  setDefaultProps: (props: Record<string, any>) => void;
}

/**
 * 内容过滤状态
 */
export interface FilterState {
  selectedCategory: string;
  selectedTag: string | null;
  searchTerm: string;
  setCategory: (category: string) => void;
  setTag: (tag: string | null) => void;
  setSearchTerm: (term: string) => void;
  reset: () => void;
}

/**
 * 缓存状态
 */
export interface CacheState {
  cache: Map<string, CacheItem>;
  set: (key: string, value: any, ttl?: number) => void;
  get: (key: string) => any | null;
  remove: (key: string) => void;
  clear: () => void;
  cleanup: () => void;
}

/**
 * 缓存项
 */
export interface CacheItem {
  value: any;
  timestamp: number;
  ttl: number;
}

// ==================== 组合状态类型 ====================

/**
 * 应用全局状态
 */
export interface AppState
  extends ThemeState,
    AuthState,
    SearchState,
    NavbarState,
    FilterState,
    CacheState {
  // 全局状态的额外属性
  initialized: boolean;
  setInitialized: (initialized: boolean) => void;
}

/**
 * 工具页面状态
 */
export interface ToolPageState extends ToolState {
  // 工具页面特有的状态
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

// ==================== 状态持久化配置 ====================

/**
 * 持久化配置
 */
export interface PersistConfig {
  name: string;
  storage?: any;
  partialize?: (state: any) => any;
  onRehydrateStorage?: (state: any) => void;
}

/**
 * 状态切片配置
 */
export interface StateSliceConfig<T> {
  name: string;
  initialState: T;
  persist?: boolean;
  persistConfig?: PersistConfig;
}

// ==================== 状态操作类型 ====================

/**
 * 状态设置器类型
 */
export type StateSetter<T> = (partial: T | Partial<T> | ((state: T) => T | Partial<T>)) => void;

/**
 * 状态获取器类型
 */
export type StateGetter<T> = () => T;

/**
 * 状态订阅器类型
 */
export type StateSubscriber<T> = (listener: (state: T, prevState: T) => void) => () => void;

/**
 * 状态存储接口
 */
export interface StateStore<T> {
  getState: StateGetter<T>;
  setState: StateSetter<T>;
  subscribe: StateSubscriber<T>;
  destroy: () => void;
}

// ==================== 类型已在上方定义并自动导出 ====================
