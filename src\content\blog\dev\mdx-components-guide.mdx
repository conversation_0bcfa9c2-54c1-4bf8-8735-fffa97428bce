---
title: MDX 组件使用指南
description: 详细介绍如何在 MDX 文档中使用各种自定义组件，包括代码块、表格、视频等
date: 2025-01-25
tags: ['MDX', '组件', '文档', '开发指南']
category: 开发
---

# MDX 组件使用指南

本文档详细介绍了项目中可用的 MDX 组件及其使用方法。这些组件经过优化，支持响应式设计、主题切换和可访问性。

## 🎯 概述

我们的 MDX 组件系统基于以下技术栈：

- **React 19** - 最新的 React 版本，支持并发特性
- **Next.js 15** - App Router 架构
- **TypeScript** - 严格类型检查
- **Tailwind CSS v4** - 现代化样式系统
- **Shiki** - 高性能代码高亮
- **shadcn/ui** - 统一的设计系统

## 📝 代码块组件

### 基础用法

```typescript
// 基础 TypeScript 代码块
function greet(name: string): string {
  return `Hello, ${name}!`;
}

const message = greet('World');
console.log(message);
```

### 带文件名的代码块

```javascript filename="utils/helpers.js"
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};
```

### 支持的语言

我们支持以下编程语言的语法高亮：

- **JavaScript/TypeScript** - `js`, `jsx`, `ts`, `tsx`
- **Web 技术** - `html`, `css`, `scss`
- **标记语言** - `markdown`, `mdx`, `json`, `yaml`, `xml`
- **Shell** - `bash`, `shell`
- **其他语言** - `python`, `java`, `go`, `rust`, `php`, `sql`
- **配置文件** - `dockerfile`, `toml`

### 代码块特性

- ✅ **语法高亮** - 基于 Shiki，支持多种主题
- ✅ **复制功能** - 一键复制代码到剪贴板
- ✅ **文件名显示** - 显示代码文件名和语言类型
- ✅ **响应式设计** - 在不同设备上自适应
- ✅ **主题适配** - 自动适配浅色和深色主题

## 📊 表格组件

### 基础表格

| 功能 | 状态 | 描述 |
|------|------|------|
| 响应式设计 | ✅ | 在移动设备上水平滚动 |
| 主题适配 | ✅ | 支持浅色和深色主题 |
| 条纹样式 | ✅ | 交替行背景色 |
| 悬停效果 | ✅ | 鼠标悬停高亮 |

### 表格特性

- ✅ **响应式滚动** - 移动端水平滚动
- ✅ **条纹样式** - 提高可读性
- ✅ **悬停高亮** - 交互反馈
- ✅ **主题适配** - 自动适配主题色彩
- ✅ **边框优化** - 清晰的视觉分隔

## 🎬 视频组件

### YouTube 视频

<Video 
  src="https://www.youtube.com/watch?v=dQw4w9WgXcQ" 
  title="YouTube 示例视频"
/>

### Bilibili 视频

<Video 
  src="https://www.bilibili.com/video/BV1xx411c7mu" 
  title="Bilibili 示例视频"
/>

### 直接视频文件

<Video 
  src="/videos/demo.mp4" 
  title="本地视频文件"
  poster="/images/video-poster.jpg"
/>

### 视频组件属性

```typescript
interface VideoProps {
  src: string;           // 视频源地址
  title?: string;        // 视频标题
  poster?: string;       // 海报图片
  autoPlay?: boolean;    // 自动播放
  controls?: boolean;    // 显示控件
  loop?: boolean;        // 循环播放
  muted?: boolean;       // 静音
  aspectRatio?: string;  // 宽高比（默认 16:9）
  lazy?: boolean;        // 懒加载（默认 true）
}
```

### 支持的视频平台

- **YouTube** - `youtube.com`, `youtu.be`
- **Bilibili** - `bilibili.com/video/`
- **Vimeo** - `vimeo.com`
- **直接文件** - `.mp4`, `.webm`, `.ogg` 等

### 视频特性

- ✅ **多平台支持** - YouTube、Bilibili、Vimeo
- ✅ **懒加载** - 点击后才加载，提升性能
- ✅ **响应式设计** - 自适应容器宽度
- ✅ **宽高比保持** - 在不同设备上保持比例
- ✅ **错误处理** - 优雅的错误提示

## 🎨 行内代码

在文本中使用行内代码：`const value = 'example'`

### 样式变体

- 默认样式：`default code`
- 主要样式：<PrimaryCode>primary code</PrimaryCode>
- 次要样式：<SecondaryCode>secondary code</SecondaryCode>
- 成功样式：<SuccessCode>success code</SuccessCode>
- 警告样式：<WarningCode>warning code</WarningCode>
- 错误样式：<ErrorCode>error code</ErrorCode>

## 🔗 链接组件

### 内部链接

[返回首页](/) - 自动识别内部链接

### 外部链接

[GitHub 仓库](https://github.com) - 自动添加外部链接图标

### 链接特性

- ✅ **自动识别** - 区分内部和外部链接
- ✅ **图标显示** - 外部链接自动添加图标
- ✅ **新窗口打开** - 外部链接在新标签页打开
- ✅ **下划线动画** - 悬停时的动画效果

## 📋 使用最佳实践

### 1. 代码块

```typescript
// ✅ 推荐：指定语言和文件名
```typescript filename="components/Button.tsx"
interface ButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
}
```

// ❌ 不推荐：没有语言标识
```
function example() {
  return 'hello';
}
```
```

### 2. 表格

```markdown
<!-- ✅ 推荐：清晰的表头和对齐 -->
| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| title | string | - | 标题文本 |
| visible | boolean | false | 是否可见 |

<!-- ❌ 不推荐：格式混乱 -->
|属性|类型|描述|
|--|--|--|
|title|string|标题|
```

### 3. 视频

```markdown
<!-- ✅ 推荐：提供标题和描述性信息 -->
<Video 
  src="https://www.youtube.com/watch?v=example" 
  title="React 组件开发教程"
/>

<!-- ❌ 不推荐：缺少描述信息 -->
<Video src="https://example.com/video" />
```

## 🎯 性能优化

### 懒加载

- 视频组件默认启用懒加载
- 代码块使用虚拟滚动（大文件）
- 图片组件支持渐进式加载

### 主题适配

- 所有组件自动适配浅色/深色主题
- 使用 CSS 变量实现主题切换
- 支持系统主题自动检测

### 可访问性

- 所有组件支持键盘导航
- 提供适当的 ARIA 标签
- 支持屏幕阅读器

## 🔧 自定义配置

### 修改默认样式

```typescript
// src/config/mdx/styles.ts
export const customStyles = {
  codeBlock: {
    theme: 'github-dark',
    showLineNumbers: true,
  },
  table: {
    striped: true,
    hover: true,
  },
  video: {
    aspectRatio: '16/9',
    lazy: true,
  },
};
```

### 添加自定义组件

```typescript
// src/components/mdx/CustomComponent.tsx
export const CustomComponent = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="custom-component">
      {children}
    </div>
  );
};

// src/config/mdx/components.ts
import { CustomComponent } from '@/components/mdx/CustomComponent';

export const MDXComponents = {
  // ... 其他组件
  CustomComponent,
};
```

## 📚 更多资源

- [MDX 官方文档](https://mdxjs.com/)
- [Next.js MDX 集成](https://nextjs.org/docs/app/building-your-application/configuring/mdx)
- [Tailwind CSS 文档](https://tailwindcss.com/)
- [shadcn/ui 组件库](https://ui.shadcn.com/)

---

这个组件系统为创建丰富的技术文档提供了强大的基础。如果你有任何问题或建议，欢迎提出 Issue 或 Pull Request！
