@import 'tailwindcss';
/* 注意：Typography 插件同时在此处和 tailwind.config.mjs 中导入 */
/* 这是必要的：tailwind.config.mjs 中导入用于配置插件，此处导入确保样式在正确位置加载 */
@plugin "@tailwindcss/typography"; /* 使用 Typography 插件提供的 prose 类 */

@custom-variant dark (&:is(.dark *));

/**
 * 主题变量映射
 *
 * 使用 @theme inline 指令将 CSS 变量映射到主题变量
 * 按照功能分组，便于维护
 */
@theme inline {
  /* 字体 */
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* 圆角 */
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  /* 主要界面颜色 */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);

  /* 主题颜色 */
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-success: var(--success);
  --color-success-foreground: var(--success-foreground);

  /* 边框和输入框 */
  --color-border: var(--border-color);
  --color-input: var(--border-color);
  --color-ring: var(--ring);

  /* 图表颜色 */
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);

  /* 侧边栏颜色 */
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--foreground);
  --color-sidebar-primary: var(--primary);
  --color-sidebar-primary-foreground: var(--primary-foreground);
  --color-sidebar-accent: var(--accent);
  --color-sidebar-accent-foreground: var(--accent-foreground);
  --color-sidebar-border: var(--border-color);
  --color-sidebar-ring: var(--ring);
}

/**
 * CSS 变量定义 - 按功能分组
 *
 * 1. 基础变量：定义基础颜色和尺寸
 * 2. 派生变量：基于基础变量计算得出
 * 3. 组件变量：特定组件使用的变量
 */

:root {
  /* ===== 基础变量 ===== */
  /* 圆角 - New York 风格使用更圆润的边角 */
  --radius: 0.75rem;

  /* 基础亮度 - neutral 基础色 */
  --light-bg: 1; /* 浅色背景亮度 */
  --light-fg: 0.145; /* 浅色前景亮度 */
  --light-surface: 0.97; /* 浅色表面亮度 */
  --light-border: 0.922; /* 浅色边框亮度 */
  --light-ring: 0.708; /* 浅色环亮度 */

  --dark-bg: 0.145; /* 深色背景亮度 */
  --dark-fg: 0.985; /* 深色前景亮度 */
  --dark-surface: 0.269; /* 深色表面亮度 */
  --dark-card: 0.205; /* 深色卡片亮度 */

  /* ===== 主题变量 - 浅色模式 ===== */
  /* 主要界面颜色 - neutral 基础色 */
  --background: oklch(var(--light-bg) 0 0);
  --foreground: oklch(var(--light-fg) 0 0);

  /* 卡片颜色 - 轻微区分于背景 */
  --card: oklch(0.98 0 0);
  --card-foreground: var(--foreground);

  /* 弹出层颜色 */
  --popover: var(--background);
  --popover-foreground: var(--foreground);

  /* 主要颜色 - New York 风格更强对比度 */
  --primary: oklch(0.25 0 0);
  --primary-foreground: oklch(0.98 0 0);

  /* 次要颜色 */
  --secondary: oklch(0.94 0 0);
  --secondary-foreground: oklch(0.35 0 0);

  /* 柔和颜色 */
  --muted: oklch(0.96 0 0);
  --muted-foreground: oklch(0.64 0 0);

  /* 强调颜色 */
  --accent: oklch(0.94 0 0);
  --accent-foreground: oklch(0.35 0 0);

  /* 破坏性操作颜色 */
  --destructive: oklch(0.65 0.18 25);
  --destructive-foreground: oklch(0.98 0 0);

  /* 成功操作颜色 */
  --success: oklch(0.65 0.15 140);
  --success-foreground: oklch(0.98 0 0);

  /* 边框和输入框 - 使用同一个基础值 */
  --border-color: oklch(0.85 0 0);
  --border: var(--border-color);
  --input: var(--border-color);
  --ring: oklch(0.7 0 0);

  /* 图表颜色 */
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);

  /* 侧边栏颜色 */
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: var(--foreground);
  --sidebar-primary: var(--primary);
  --sidebar-primary-foreground: var(--primary-foreground);
  --sidebar-accent: var(--accent);
  --sidebar-accent-foreground: var(--accent-foreground);
  --sidebar-border: var(--border);
  --sidebar-ring: var(--ring);
}

.dark {
  /* ===== 主题变量 - 深色模式 ===== */
  /* 主要界面颜色 - neutral 基础色 */
  --background: oklch(0.12 0 0);
  --foreground: oklch(0.98 0 0);

  /* 卡片颜色 - 轻微区分于背景 */
  --card: oklch(0.16 0 0);
  --card-foreground: var(--foreground);

  /* 弹出层颜色 */
  --popover: oklch(0.16 0 0);
  --popover-foreground: var(--foreground);

  /* 主要颜色 - New York 风格更强对比度 */
  --primary: oklch(0.9 0 0);
  --primary-foreground: oklch(0.12 0 0);

  /* 次要颜色 */
  --secondary: oklch(0.2 0 0);
  --secondary-foreground: oklch(0.9 0 0);

  /* 柔和颜色 */
  --muted: oklch(0.18 0 0);
  --muted-foreground: oklch(0.7 0 0);

  /* 强调颜色 */
  --accent: oklch(0.2 0 0);
  --accent-foreground: oklch(0.9 0 0);

  /* 破坏性操作颜色 */
  --destructive: oklch(0.7 0.18 25);
  --destructive-foreground: oklch(0.98 0 0);

  /* 成功操作颜色 */
  --success: oklch(0.7 0.15 140);
  --success-foreground: oklch(0.98 0 0);

  /* 边框和输入框 - 使用同一个基础值 */
  --border-color: oklch(0.3 0 0);
  --border: var(--border-color);
  --input: var(--border-color);
  --ring: oklch(0.5 0 0);

  /* 图表颜色 */
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);

  /* 侧边栏颜色 */
  --sidebar: var(--card);
  --sidebar-foreground: var(--foreground);
  --sidebar-primary: var(--chart-1);
  --sidebar-primary-foreground: var(--foreground);
  --sidebar-accent: var(--secondary);
  --sidebar-accent-foreground: var(--foreground);
  --sidebar-border: var(--border);
  --sidebar-ring: var(--ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    /* 系统字体回退方案 */
    font-family:
      var(--font-sans),
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      Roboto,
      'Helvetica Neue',
      Arial,
      sans-serif;
  }
  kbd,
  samp {
    /* 系统等宽字体回退方案 */
    font-family:
      var(--font-mono), SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New',
      monospace;
  }
}
/* 首页特殊处理 - 隐藏滚动条和页脚样式 */
body.home-page {
  overflow: hidden;
}

/* ===== MDX 颜色系统 ===== */
:root {
  /* MDX 专用颜色变量 */
  --mdx-link-color: var(--primary);
  --mdx-link-hover: var(--primary-hover);
  --mdx-code-bg: var(--muted);
  --mdx-code-color: var(--foreground);
  --mdx-blockquote-color: var(--muted-foreground);
  --mdx-blockquote-border: var(--border);
  --mdx-table-border: var(--border);
  --mdx-table-header-bg: var(--muted);
  --mdx-image-shadow: var(--shadow-md);
}

.dark {
  --mdx-code-bg: var(--muted-dark);
  --mdx-image-shadow: none;
}

/* MDX Typography 主题变量映射 */
.prose {
  --tw-prose-body: var(--foreground);
  --tw-prose-headings: var(--foreground);
  --tw-prose-links: var(--mdx-link-color);
  --tw-prose-bold: var(--foreground);
  --tw-prose-counters: var(--muted-foreground);
  --tw-prose-bullets: var(--muted-foreground);
  --tw-prose-hr: var(--border);
  --tw-prose-quotes: var(--mdx-blockquote-color);
  --tw-prose-quote-borders: var(--mdx-blockquote-border);
  --tw-prose-captions: var(--muted-foreground);
  --tw-prose-th-borders: var(--mdx-table-border);
  --tw-prose-td-borders: var(--mdx-table-border);
}

/* 深色模式覆盖 */
.dark .prose {
  --tw-prose-links: var(--mdx-link-color);
  --tw-prose-bold: var(--foreground);
}
