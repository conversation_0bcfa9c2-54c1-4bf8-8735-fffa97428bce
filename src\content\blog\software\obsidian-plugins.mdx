---
title: Obsidian 实用插件推荐
description: 精选实用的 Obsidian 插件，包括访问加速和功能增强插件
date: 2025-01-15
category: 软件
tags:
- Obsidian
- 插件
- 知识管理
- 效率工具
---

## 加速访问

### [obsidian-pkmer](https://pkmer.cn/products/market/)

无需科学上网，轻松访问和下载 Obsidian 插件和主题。

## 功能补足

### [obsidian-pandoc](https://github.com/OliverBalfour/obsidian-pandoc)

为 Obsidian 提供更多导出格式，可通过"pandoc"命令调出。

![pandoc](https://img.dava.cc/img/pandoc.png)

### [copy-url-in-preview](https://github.com/NomarCub/obsidian-copy-url-in-preview)

为 Obsidian 中的本地图像提供更多选项。

![obsidian图片菜单](https://img.dava.cc/img/obsidian图片菜单.png)

### [editing-toolbar](https://github.com/PKM-er/obsidian-editing-toolbar)

给 Obsidian 增加一个文本编辑器工具栏，使其像富文本编辑器一样，所见即所得。

![editing-toolbar](https://img.dava.cc/img/editing-toolbar.png)

此外还支持置顶工具栏、自定义图标顺序、光标跟随等。

### [obsidian-mind-map](https://github.com/lynchjames/obsidian-mind-map)

以思维导图的方式展示笔记，可通过"Mind Map"命令调出。

### [obsidian-excalidraw-plugin](https://github.com/zsviczian/obsidian-excalidraw-plugin)

将 [Excalidraw](https://excalidraw.com/) 草图工具集成到 Obsidian 中。

## 体验优化

### [easy-typing-obsidian](https://github.com/Yaozhuwa/easy-typing-obsidian)

自动格式化文本：如首字母大写，文字与字母、数字间自动添加空格等。

符号编辑增强：如符号自动配对/删除及其他 Obsidian 语法相关的编辑增强。感知最强的应该是插入双链笔记时，输入两个 `【` 自动变成 `[[]]`）。

### [obsidian-heading-shifter](https://github.com/k4a-l/obsidian-heading-shifter)

通过 Shift 和 Shift+Tab 快捷键快速更改标题层级。

### [obsidian-auto-link-title](https://github.com/zolrath/obsidian-auto-link-title)

粘贴链接时自动获取链接标题。

### [file-explorer-note-count](https://github.com/ozntel/file-explorer-note-count)

在 Obsidian 文件资源管理器中增加计数功能。

![note-count](https://img.dava.cc/img/note-count.png)

## 高级功能

### [obsidian-custom-frames](https://github.com/Ellpeck/ObsidianCustomFrames)

将你喜欢的网页嵌入到 Obsidian 中。

### [obsidian-image-auto-upload-plugin](https://github.com/eust-w/obsidian-image-auto-upload)

配合 PicGo/PicList 之类的图传管理工具，在 Obsidian 中粘贴图片时，自动上传到图床。

### [obsidian-paste-image-rename](https://github.com/reorx/obsidian-paste-image-rename)

在 Obsidian 中粘贴图片时重命名。