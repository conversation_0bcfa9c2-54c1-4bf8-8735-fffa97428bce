/**
 * 标题项类型
 */
export interface Heading {
  /** 标题ID */
  id: string;
  /** 标题文本 */
  text: string;
  /** 标题级别 (1-4) */
  level: number;
}

/**
 * 从 Markdown/MDX 内容中提取标题并更新内容
 * @param content Markdown/MDX 内容
 * @returns 标题列表和处理后的内容
 */
export function extractHeadings(content: string): {
  headings: Heading[];
  processedContent: string;
} {
  const headings: Heading[] = [];
  const headingRegex = /^(#{1,4})\s+(.+?)(?:\s*{#([\w-]+)})?$/gm;
  let match;
  let processedContent = content;

  // 首先提取所有标题
  while ((match = headingRegex.exec(content)) !== null) {
    const level = match[1].length;
    const text = match[2].trim();

    // 处理不同类型的标题文本
    let finalText = text;

    // 解析markdown链接格式 [text](url)
    const linkMatch = text.match(/\[([^\]]+)\]\([^)]+\)/);
    if (linkMatch) {
      finalText = linkMatch[1];
    } else {
      // 处理行内代码格式 `code` - 移除反引号但保留内容
      finalText = text.replace(/`([^`]+)`/g, '$1');
    }

    const customId = match[3];
    const id =
      customId ||
      `heading-${finalText
        .toLowerCase()
        .replace(/\s+/g, '-')
        .replace(/[^\w-]/g, '')}-${match.index}`;

    if (level >= 1 && level <= 4) {
      headings.push({ id, text: finalText, level });
    }
  }

  // 确保所有标题都有唯一ID
  headings.forEach(heading => {
    // 转义特殊字符，但保留反引号的处理
    const escapedText = heading.text.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&');

    // 构建更灵活的正则表达式，支持行内代码和链接
    const patterns = [
      // 匹配带链接的标题: ## [text](url)
      `^(#{${heading.level}})\\s+\\[[^\\]]+\\]\\([^)]+\\)(?:\\s*{#[\\w-]+})?$`,
      // 匹配带行内代码的标题: ## `code`
      `^(#{${heading.level}})\\s+\`[^\`]+\`(?:\\s*{#[\\w-]+})?$`,
      // 匹配普通标题
      `^(#{${heading.level}})\\s+${escapedText}(?:\\s*{#[\\w-]+})?$`,
    ];

    for (const pattern of patterns) {
      const headingRegex = new RegExp(pattern, 'gm');
      const match = headingRegex.exec(processedContent);
      if (match) {
        processedContent = processedContent.replace(
          headingRegex,
          `${match[1]} ${match[0]
            .split(' ')
            .slice(1)
            .join(' ')
            .replace(/\s*{#[\w-]+}$/, '')} {#${heading.id}}`
        );
        break;
      }
    }
  });

  return {
    headings,
    processedContent,
  };
}
