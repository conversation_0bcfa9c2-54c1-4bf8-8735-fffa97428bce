/**
 * 工具页面通用处理函数
 * 提供各种工具的数据处理和转换功能
 */

// ==================== 类型定义 ====================

export interface ProcessResult {
  success: boolean;
  data?: string;
  error?: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors?: string[];
  warnings?: string[];
}

// ==================== JSON 处理工具 ====================

/**
 * JSON 处理工具集
 */
export const jsonUtils = {
  /**
   * 格式化 JSON
   */
  format: (input: string): ProcessResult => {
    try {
      if (!input.trim()) {
        return { success: false, error: '请输入 JSON 数据' };
      }
      const parsed = JSON.parse(input);
      const formatted = JSON.stringify(parsed, null, 2);
      return { success: true, data: formatted };
    } catch (error) {
      return {
        success: false,
        error: `JSON 格式错误: ${error instanceof Error ? error.message : '未知错误'}`,
      };
    }
  },

  /**
   * 压缩 JSON
   */
  minify: (input: string): ProcessResult => {
    try {
      if (!input.trim()) {
        return { success: false, error: '请输入 JSON 数据' };
      }
      const parsed = JSON.parse(input);
      const minified = JSON.stringify(parsed);
      return { success: true, data: minified };
    } catch (error) {
      return {
        success: false,
        error: `JSON 格式错误: ${error instanceof Error ? error.message : '未知错误'}`,
      };
    }
  },

  /**
   * 验证 JSON
   */
  validate: (input: string): ProcessResult => {
    try {
      if (!input.trim()) {
        return { success: false, error: '请输入 JSON 数据' };
      }
      JSON.parse(input);
      return { success: true, data: 'JSON 格式正确' };
    } catch (error) {
      return {
        success: false,
        error: `JSON 格式错误: ${error instanceof Error ? error.message : '未知错误'}`,
      };
    }
  },

  /**
   * JSON 转 CSV
   */
  toCSV: (input: string): ProcessResult => {
    try {
      if (!input.trim()) {
        return { success: false, error: '请输入 JSON 数据' };
      }
      const parsed = JSON.parse(input);

      if (!Array.isArray(parsed)) {
        return { success: false, error: 'JSON 必须是数组格式才能转换为 CSV' };
      }

      if (parsed.length === 0) {
        return { success: false, error: 'JSON 数组不能为空' };
      }

      // 获取所有键作为表头
      const headers = Object.keys(parsed[0]);
      const csvHeaders = headers.join(',');

      // 转换数据行
      const csvRows = parsed.map(row =>
        headers
          .map(header => {
            const value = row[header];
            // 处理包含逗号或引号的值
            if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
              return `"${value.replace(/"/g, '""')}"`;
            }
            return value;
          })
          .join(',')
      );

      const csv = [csvHeaders, ...csvRows].join('\n');
      return { success: true, data: csv };
    } catch (error) {
      return {
        success: false,
        error: `转换失败: ${error instanceof Error ? error.message : '未知错误'}`,
      };
    }
  },
};

// ==================== Base64 编解码工具 ====================

/**
 * Base64 编解码工具集
 */
export const base64Utils = {
  /**
   * 编码为 Base64
   */
  encode: (input: string): ProcessResult => {
    try {
      if (!input) {
        return { success: false, error: '请输入要编码的文本' };
      }
      const encoded = btoa(unescape(encodeURIComponent(input)));
      return { success: true, data: encoded };
    } catch (error) {
      return {
        success: false,
        error: `编码失败: ${error instanceof Error ? error.message : '未知错误'}`,
      };
    }
  },

  /**
   * 从 Base64 解码
   */
  decode: (input: string): ProcessResult => {
    try {
      if (!input.trim()) {
        return { success: false, error: '请输入要解码的 Base64 数据' };
      }
      const decoded = decodeURIComponent(escape(atob(input)));
      return { success: true, data: decoded };
    } catch (error) {
      return {
        success: false,
        error: `解码失败: ${error instanceof Error ? error.message : '请检查 Base64 格式是否正确'}`,
      };
    }
  },
};

// ==================== URL 编解码工具 ====================

/**
 * URL 编解码工具集
 */
export const urlUtils = {
  /**
   * URL 编码
   */
  encode: (input: string): ProcessResult => {
    try {
      if (!input) {
        return { success: false, error: '请输入要编码的 URL' };
      }
      const encoded = encodeURIComponent(input);
      return { success: true, data: encoded };
    } catch (error) {
      return {
        success: false,
        error: `编码失败: ${error instanceof Error ? error.message : '未知错误'}`,
      };
    }
  },

  /**
   * URL 解码
   */
  decode: (input: string): ProcessResult => {
    try {
      if (!input.trim()) {
        return { success: false, error: '请输入要解码的 URL' };
      }
      const decoded = decodeURIComponent(input);
      return { success: true, data: decoded };
    } catch (error) {
      return {
        success: false,
        error: `解码失败: ${error instanceof Error ? error.message : '请检查 URL 格式是否正确'}`,
      };
    }
  },
};

// ==================== HTML 编解码工具 ====================

/**
 * HTML 编解码工具集
 */
export const htmlUtils = {
  /**
   * HTML 编码
   */
  encode: (input: string): ProcessResult => {
    if (!input) {
      return { success: false, error: '请输入要编码的 HTML' };
    }

    const encoded = input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');

    return { success: true, data: encoded };
  },

  /**
   * HTML 解码
   */
  decode: (input: string): ProcessResult => {
    if (!input.trim()) {
      return { success: false, error: '请输入要解码的 HTML' };
    }

    const decoded = input
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'");

    return { success: true, data: decoded };
  },
};

// ==================== 文本处理工具 ====================

/**
 * 文本处理工具集
 */
export const textUtils = {
  /**
   * 转换为大写
   */
  toUpperCase: (input: string): ProcessResult => {
    if (!input) {
      return { success: false, error: '请输入文本' };
    }
    return { success: true, data: input.toUpperCase() };
  },

  /**
   * 转换为小写
   */
  toLowerCase: (input: string): ProcessResult => {
    if (!input) {
      return { success: false, error: '请输入文本' };
    }
    return { success: true, data: input.toLowerCase() };
  },

  /**
   * 移除空格
   */
  removeSpaces: (input: string): ProcessResult => {
    if (!input) {
      return { success: false, error: '请输入文本' };
    }
    return { success: true, data: input.replace(/\s+/g, '') };
  },

  /**
   * 统计文本信息
   */
  wordCount: (input: string): ProcessResult => {
    if (!input) {
      return { success: false, error: '请输入文本' };
    }

    const words = input
      .trim()
      .split(/\s+/)
      .filter(word => word.length > 0);
    const chars = input.length;
    const charsNoSpaces = input.replace(/\s/g, '').length;
    const lines = input.split('\n').length;
    const paragraphs = input.split(/\n\s*\n/).filter(p => p.trim().length > 0).length;

    const result = `字符数: ${chars}
字符数(不含空格): ${charsNoSpaces}
单词数: ${words.length}
行数: ${lines}
段落数: ${paragraphs}`;

    return { success: true, data: result };
  },

  /**
   * 首字母大写
   */
  capitalize: (input: string): ProcessResult => {
    if (!input) {
      return { success: false, error: '请输入文本' };
    }

    const capitalized = input.replace(/\b\w/g, char => char.toUpperCase());
    return { success: true, data: capitalized };
  },

  /**
   * 反转文本
   */
  reverse: (input: string): ProcessResult => {
    if (!input) {
      return { success: false, error: '请输入文本' };
    }

    const reversed = input.split('').reverse().join('');
    return { success: true, data: reversed };
  },
};

// ==================== 颜色处理工具 ====================

/**
 * 颜色处理工具集
 */
export const colorUtils = {
  /**
   * HEX 转 RGB
   */
  hexToRgb: (hex: string): { r: number; g: number; b: number } | null => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16),
        }
      : null;
  },

  /**
   * RGB 转 HEX
   */
  rgbToHex: (r: number, g: number, b: number): string => {
    return (
      '#' +
      [r, g, b]
        .map(x => {
          const hex = x.toString(16);
          return hex.length === 1 ? '0' + hex : hex;
        })
        .join('')
    );
  },

  /**
   * HEX 转 HSL
   */
  hexToHsl: (hex: string): { h: number; s: number; l: number } | null => {
    const rgb = colorUtils.hexToRgb(hex);
    if (!rgb) return null;

    const { r, g, b } = rgb;
    const rNorm = r / 255;
    const gNorm = g / 255;
    const bNorm = b / 255;

    const max = Math.max(rNorm, gNorm, bNorm);
    const min = Math.min(rNorm, gNorm, bNorm);
    let h = 0;
    let s = 0;
    const l = (max + min) / 2;

    if (max !== min) {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

      switch (max) {
        case rNorm:
          h = (gNorm - bNorm) / d + (gNorm < bNorm ? 6 : 0);
          break;
        case gNorm:
          h = (bNorm - rNorm) / d + 2;
          break;
        case bNorm:
          h = (rNorm - gNorm) / d + 4;
          break;
      }
      h /= 6;
    }

    return {
      h: Math.round(h * 360),
      s: Math.round(s * 100),
      l: Math.round(l * 100),
    };
  },

  /**
   * 生成随机颜色
   */
  generateRandomColor: (): string => {
    const randomHex = Math.floor(Math.random() * 16777215)
      .toString(16)
      .padStart(6, '0');
    return `#${randomHex}`;
  },

  /**
   * 获取颜色的所有格式
   */
  getColorFormats: (hex: string) => {
    const rgb = colorUtils.hexToRgb(hex);
    const hsl = colorUtils.hexToHsl(hex);

    return {
      hex: hex.toUpperCase(),
      rgb: rgb ? `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})` : '',
      hsl: hsl ? `hsl(${hsl.h}, ${hsl.s}%, ${hsl.l}%)` : '',
      css: `--color: ${hex};`,
    };
  },
};

// ==================== 计算器工具 ====================

/**
 * 计算器工具集
 */
export const calculatorUtils = {
  /**
   * 安全的表达式求值
   */
  evaluate: (expression: string): ProcessResult => {
    try {
      if (!expression.trim()) {
        return { success: false, error: '请输入表达式' };
      }

      // 清理表达式，替换特殊符号
      const cleanExpression = expression.replace(/×/g, '*').replace(/÷/g, '/').replace(/\s+/g, '');

      // 基本安全检查
      if (!/^[0-9+\-*/().\s]+$/.test(cleanExpression)) {
        return { success: false, error: '表达式包含无效字符' };
      }

      // 检查括号匹配
      const openBrackets = (cleanExpression.match(/\(/g) || []).length;
      const closeBrackets = (cleanExpression.match(/\)/g) || []).length;
      if (openBrackets !== closeBrackets) {
        return { success: false, error: '括号不匹配' };
      }

      // 使用 Function 构造器进行安全求值
      const result = new Function('return ' + cleanExpression)();

      if (typeof result !== 'number' || !isFinite(result)) {
        return { success: false, error: '计算结果无效' };
      }

      // 格式化结果
      const formattedResult = Number(result.toFixed(10)).toString();
      return { success: true, data: formattedResult };
    } catch (error) {
      return {
        success: false,
        error: `计算错误: ${error instanceof Error ? error.message : '未知错误'}`,
      };
    }
  },

  /**
   * 格式化数字显示
   */
  formatNumber: (num: number): string => {
    if (num === 0) return '0';
    if (Math.abs(num) < 0.000001) return num.toExponential(2);
    if (Math.abs(num) > 999999999) return num.toExponential(2);

    // 移除尾随零
    return parseFloat(num.toFixed(10)).toString();
  },

  /**
   * 角度转弧度
   */
  degToRad: (degrees: number): number => {
    return (degrees * Math.PI) / 180;
  },

  /**
   * 弧度转角度
   */
  radToDeg: (radians: number): number => {
    return (radians * 180) / Math.PI;
  },
};
