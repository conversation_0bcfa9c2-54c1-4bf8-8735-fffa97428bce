'use client';

import React, { type HTMLAttributes, useState, useCallback, forwardRef } from 'react';
import { Copy, Check } from 'lucide-react';
import { cn } from '@/utils';

/**
 * MDX 代码块组件属性
 */
export interface MDXCodeBlockProps extends HTMLAttributes<HTMLPreElement> {
  /** 代码内容 */
  children?: React.ReactNode;
  /** 代码语言类名 */
  className?: string;
  /** 文件名 */
  filename?: string;
  /** 是否显示行号 */
  showLineNumbers?: boolean;
  /** 高亮的行号 */
  highlightLines?: number[];
  /** 是否启用复制功能 */
  copyable?: boolean;
}

/**
 * 代码语言映射和显示名称
 */
const LANGUAGE_MAP: Record<string, { name: string; icon?: string }> = {
  javascript: { name: 'JavaScript', icon: '🟨' },
  typescript: { name: 'TypeScript', icon: '🔷' },
  jsx: { name: 'React JSX', icon: '⚛️' },
  tsx: { name: 'React TSX', icon: '⚛️' },
  json: { name: 'JSON', icon: '📄' },
  html: { name: 'HTML', icon: '🌐' },
  css: { name: 'CSS', icon: '🎨' },
  scss: { name: 'SCSS', icon: '🎨' },
  markdown: { name: 'Markdown', icon: '📝' },
  mdx: { name: 'MDX', icon: '📝' },
  bash: { name: 'Bash', icon: '💻' },
  shell: { name: 'Shell', icon: '💻' },
  python: { name: 'Python', icon: '🐍' },
  java: { name: 'Java', icon: '☕' },
  go: { name: 'Go', icon: '🐹' },
  rust: { name: 'Rust', icon: '🦀' },
  php: { name: 'PHP', icon: '🐘' },
  sql: { name: 'SQL', icon: '🗄️' },
  yaml: { name: 'YAML', icon: '📋' },
  toml: { name: 'TOML', icon: '📋' },
  xml: { name: 'XML', icon: '📄' },
  dockerfile: { name: 'Dockerfile', icon: '🐳' },
};

/**
 * 代码块样式配置
 */
const codeBlockStyles = {
  container: cn(
    'group relative my-6 overflow-hidden',
    'rounded-xl border border-border/50',
    'bg-muted/30 dark:bg-muted/20',
    'shadow-sm hover:shadow-md transition-shadow duration-200'
  ),

  header: cn(
    'flex items-center justify-between',
    'px-4 py-2 border-b border-border/50',
    'bg-muted/50 dark:bg-muted/30',
    'text-sm font-medium text-muted-foreground'
  ),

  languageInfo: cn('flex items-center gap-2', 'text-xs font-mono'),

  copyButton: cn(
    'inline-flex items-center gap-1.5 px-2 py-1',
    'text-xs font-medium',
    'rounded-md border border-border/50',
    'bg-background/50 hover:bg-background',
    'text-muted-foreground hover:text-foreground',
    'transition-all duration-200',
    'opacity-0 group-hover:opacity-100',
    'focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring'
  ),

  pre: cn(
    'overflow-x-auto p-4',
    'text-sm leading-relaxed',
    'bg-transparent',
    // 确保 Shiki 样式不被覆盖
    '[&>code]:bg-transparent [&>code]:p-0',
    '[&>code]:font-mono [&>code]:text-sm',
    // 保留 Shiki 的内联样式，不强制覆盖颜色
    '[&>code]:!text-[unset] [&>code]:!color-[unset]',
    '[&>code>span]:!text-[unset] [&>code>span]:!color-[unset]'
  ),

  code: cn(
    'font-mono text-sm',
    'bg-transparent',
    // 不设置文本颜色，让 Shiki 的内联样式生效
    '!text-[unset] !color-[unset]'
  ),
};

/**
 * 复制代码到剪贴板
 */
const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch {
    // 降级方案
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.opacity = '0';
    document.body.appendChild(textArea);
    textArea.select();
    const success = document.execCommand('copy');
    document.body.removeChild(textArea);
    return success;
  }
};

/**
 * 提取代码文本内容
 */
const extractCodeText = (children: React.ReactNode): string => {
  if (typeof children === 'string') {
    return children;
  }

  if (React.isValidElement(children)) {
    const props = children.props as { children?: React.ReactNode };
    if (props?.children) {
      return extractCodeText(props.children);
    }
  }

  if (Array.isArray(children)) {
    return children.map(extractCodeText).join('');
  }

  return String(children || '');
};

/**
 * 解析语言信息
 */
const parseLanguage = (className?: string) => {
  if (!className) return null;

  const match = className.match(/language-(\w+)/);
  if (!match) return null;

  const lang = match[1].toLowerCase();
  return LANGUAGE_MAP[lang] || { name: lang.toUpperCase() };
};

/**
 * MDX 代码块组件
 *
 * 功能：
 * 1. 语法高亮显示（通过 Shiki 处理）
 * 2. 代码复制功能
 * 3. 语言标识和文件名显示
 * 4. 响应式设计
 * 5. 主题适配
 */
export const MDXCodeBlock = forwardRef<HTMLPreElement, MDXCodeBlockProps>(
  (
    {
      children,
      className,
      filename,
      showLineNumbers: _showLineNumbers = false,
      highlightLines: _highlightLines = [],
      copyable = true,
      ...props
    },
    ref
  ) => {
    const [copied, setCopied] = useState(false);

    // 解析语言信息
    const languageInfo = parseLanguage(className);

    // 提取代码文本
    const codeText = extractCodeText(children);

    // 复制处理函数
    const handleCopy = useCallback(async () => {
      if (!codeText.trim()) return;

      const success = await copyToClipboard(codeText);
      if (success) {
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      }
    }, [codeText]);

    // 构建头部信息
    const showHeader = filename || languageInfo || copyable;

    return (
      <div className={codeBlockStyles.container}>
        {showHeader && (
          <div className={codeBlockStyles.header}>
            <div className={codeBlockStyles.languageInfo}>
              {languageInfo?.icon && <span>{languageInfo.icon}</span>}
              <span>{filename || languageInfo?.name || 'Code'}</span>
            </div>

            {copyable && codeText.trim() && (
              <button
                type="button"
                onClick={handleCopy}
                className={codeBlockStyles.copyButton}
                aria-label={copied ? 'Copied!' : 'Copy code'}
              >
                {copied ? (
                  <>
                    <Check className="h-3 w-3" />
                    <span>Copied!</span>
                  </>
                ) : (
                  <>
                    <Copy className="h-3 w-3" />
                    <span>Copy</span>
                  </>
                )}
              </button>
            )}
          </div>
        )}

        <pre ref={ref} className={cn(codeBlockStyles.pre, className)} {...props}>
          <code className={codeBlockStyles.code}>{children}</code>
        </pre>
      </div>
    );
  }
);

MDXCodeBlock.displayName = 'MDXCodeBlock';

export default MDXCodeBlock;
