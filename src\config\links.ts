/**
 * 链接导航相关配置
 * 统一管理链接分类、链接数据和相关配置
 */

// ==================== 类型定义 ====================

export type CategoryId =
  | 'ai'
  | 'development'
  | 'design'
  | 'audio'
  | 'video'
  | 'office'
  | 'productivity';

export interface Category {
  id: CategoryId;
  name: string;
  description: string;
  order: number;
}

export interface Item {
  id: string;
  title: string;
  description: string;
  url: string;
  icon: string;
  iconType: 'image' | 'emoji';
  tags: string[];
  featured: boolean;
  category: CategoryId;
  createdAt: string;
  updatedAt: string;
}

// ==================== 链接分类配置 ====================

export const LINK_CATEGORIES: Category[] = [
  {
    id: 'ai',
    name: '人工智能',
    description: '分享AIGC与实用技巧',
    order: 1,
  },
  {
    id: 'development',
    name: '开发工具',
    description: '编程开发相关工具和资源',
    order: 2,
  },
  {
    id: 'design',
    name: '设计资源',
    description: '设计工具和素材资源',
    order: 3,
  },
  {
    id: 'audio',
    name: '音频工具',
    description: '音频处理和音乐相关工具',
    order: 4,
  },
  {
    id: 'video',
    name: '视频工具',
    description: '视频编辑和处理工具',
    order: 5,
  },
  {
    id: 'office',
    name: '办公工具',
    description: '办公效率和文档处理工具',
    order: 6,
  },
  {
    id: 'productivity',
    name: '效率工具',
    description: '提升工作效率的各类工具',
    order: 7,
  },
];

// ==================== 链接配置常量 ====================

/**
 * 链接页面配置
 */
export const LINKS_CONFIG = {
  /** 每页显示的链接数量 */
  ITEMS_PER_PAGE: 24,
  /** 默认分类 */
  DEFAULT_CATEGORY: 'all',
  /** 是否启用搜索 */
  ENABLE_SEARCH: true,
  /** 是否启用分类过滤 */
  ENABLE_CATEGORY_FILTER: true,
  /** 是否启用标签过滤 */
  ENABLE_TAG_FILTER: true,
  /** 是否显示特色标记 */
  SHOW_FEATURED: true,
  /** 是否显示描述 */
  SHOW_DESCRIPTION: true,
  /** 卡片布局模式 */
  CARD_LAYOUT: 'grid' as const,
  /** 网格列数配置 */
  GRID_COLUMNS: {
    mobile: 1,
    tablet: 2,
    desktop: 3,
    large: 4,
  },
} as const;

/**
 * 图标配置
 */
export const ICON_CONFIG = {
  /** 默认图标 */
  DEFAULT_ICON: '🔗',
  /** 图标尺寸 */
  ICON_SIZE: {
    small: 16,
    medium: 24,
    large: 32,
  },
  /** 支持的图标类型 */
  SUPPORTED_TYPES: ['image', 'emoji'] as const,
  /** 图标缓存时间 */
  CACHE_TIME: 24 * 60 * 60 * 1000, // 24小时
} as const;

/**
 * 网站解析配置
 */
export const PARSER_CONFIG = {
  /** 请求超时时间 */
  TIMEOUT: 10000,
  /** 最大重试次数 */
  MAX_RETRIES: 3,
  /** 用户代理 */
  USER_AGENT: 'Mozilla/5.0 (compatible; LinkParser/1.0)',
  /** 支持的图片格式 */
  SUPPORTED_IMAGE_FORMATS: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'],
  /** 最大图片大小 (bytes) */
  MAX_IMAGE_SIZE: 5 * 1024 * 1024, // 5MB
} as const;

// ==================== 工具函数 ====================

/**
 * 根据分类ID获取分类信息
 */
export function getLinkCategory(categoryId: string): Category | undefined {
  return LINK_CATEGORIES.find(category => category.id === categoryId);
}

/**
 * 获取所有分类，按顺序排序
 */
export function getAllCategories(): Category[] {
  return [...LINK_CATEGORIES].sort((a, b) => a.order - b.order);
}

/**
 * 验证链接URL格式
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * 提取域名
 */
export function extractDomain(url: string): string {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch {
    return '';
  }
}

/**
 * 生成链接ID
 */
export function generateLinkId(title: string, url: string): string {
  const domain = extractDomain(url);
  const slug = title
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '-')
    .replace(/-+/g, '-');
  return `${domain}-${slug}`.substring(0, 50);
}

/**
 * 格式化链接数据
 */
export function formatLinkItem(item: Partial<Item>): Item {
  const now = new Date().toISOString();

  return {
    id: item.id || generateLinkId(item.title || '', item.url || ''),
    title: item.title || '',
    description: item.description || '',
    url: item.url || '',
    icon: item.icon || ICON_CONFIG.DEFAULT_ICON,
    iconType: item.iconType || 'emoji',
    tags: item.tags || [],
    featured: item.featured || false,
    category: item.category || 'productivity',
    createdAt: item.createdAt || now,
    updatedAt: now,
  };
}

/**
 * 搜索链接
 */
export function searchLinks(items: Item[], query: string): Item[] {
  if (!query.trim()) return items;

  const lowerQuery = query.toLowerCase();

  return items.filter(
    item =>
      item.title.toLowerCase().includes(lowerQuery) ||
      item.description.toLowerCase().includes(lowerQuery) ||
      item.tags.some(tag => tag.toLowerCase().includes(lowerQuery)) ||
      extractDomain(item.url).toLowerCase().includes(lowerQuery)
  );
}

/**
 * 按分类过滤链接
 */
export function filterLinksByCategory(items: Item[], categoryId: string): Item[] {
  if (categoryId === 'all') return items;
  return items.filter(item => item.category === categoryId);
}

/**
 * 按标签过滤链接
 */
export function filterLinksByTag(items: Item[], tag: string): Item[] {
  if (!tag) return items;
  return items.filter(item => item.tags.includes(tag));
}

/**
 * 获取所有标签
 */
export function getAllTags(items: Item[]): string[] {
  const tags = new Set<string>();
  items.forEach(item => {
    item.tags.forEach(tag => tags.add(tag));
  });
  return Array.from(tags).sort();
}

/**
 * 获取特色链接
 */
export function getFeaturedLinks(items: Item[]): Item[] {
  return items.filter(item => item.featured);
}

/**
 * 按分类分组链接
 */
export function groupLinksByCategory(items: Item[]): Record<string, Item[]> {
  const groups: Record<string, Item[]> = {};

  items.forEach(item => {
    if (!groups[item.category]) {
      groups[item.category] = [];
    }
    groups[item.category].push(item);
  });

  return groups;
}

// ==================== 数据验证 ====================

/**
 * 验证链接项数据
 */
export function validateLinkItem(item: Partial<Item>): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!item.title?.trim()) {
    errors.push('标题不能为空');
  }

  if (!item.url?.trim()) {
    errors.push('URL不能为空');
  } else if (!isValidUrl(item.url)) {
    errors.push('URL格式不正确');
  }

  if (!item.category) {
    errors.push('必须选择分类');
  } else if (!LINK_CATEGORIES.find(cat => cat.id === item.category)) {
    errors.push('分类不存在');
  }

  if (item.iconType && !ICON_CONFIG.SUPPORTED_TYPES.includes(item.iconType as any)) {
    errors.push('不支持的图标类型');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// ==================== 示例链接数据 ====================

/**
 * 示例链接数据
 * 实际项目中应该从数据库或API获取
 */
export const SAMPLE_LINKS: Item[] = [
  {
    id: 'chatgpt',
    title: 'ChatGPT',
    description: 'OpenAI开发的强大AI对话助手',
    url: 'https://chat.openai.com',
    icon: '🤖',
    iconType: 'emoji',
    tags: ['AI', '对话', '写作'],
    featured: true,
    category: 'ai',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 'github',
    title: 'GitHub',
    description: '全球最大的代码托管平台',
    url: 'https://github.com',
    icon: '🐙',
    iconType: 'emoji',
    tags: ['代码', '开源', '协作'],
    featured: true,
    category: 'development',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 'figma',
    title: 'Figma',
    description: '协作式界面设计工具',
    url: 'https://figma.com',
    icon: '🎨',
    iconType: 'emoji',
    tags: ['设计', 'UI', '协作'],
    featured: true,
    category: 'design',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 'notion',
    title: 'Notion',
    description: '一体化工作空间',
    url: 'https://notion.so',
    icon: '📝',
    iconType: 'emoji',
    tags: ['笔记', '协作', '管理'],
    featured: false,
    category: 'productivity',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
];

/**
 * 链接数据对象（兼容原有结构）
 */
export const links = {
  categories: LINK_CATEGORIES,
  items: SAMPLE_LINKS,
};

// ==================== 类型导出 ====================

export type LinksConfig = typeof LINKS_CONFIG;
export type IconConfig = typeof ICON_CONFIG;
export type ParserConfig = typeof PARSER_CONFIG;
