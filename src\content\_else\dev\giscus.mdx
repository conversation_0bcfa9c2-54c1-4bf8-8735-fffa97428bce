---
title: Giscus 评论系统集成指南
description: 详细介绍如何在 React 应用中集成 Giscus 评论功能，包括环境配置、组件开发、GitHub Discussions 设置和主题定制等完整实施方案
date: "2024-01-09"
category: "功能集成"
tags:
- 评论系统
- GitHub集成
- React组件
- 用户交互
- 开源方案
---

## Giscus 配置指南

```bash:/terminal
# 安装 giscus-react 包
pnpm add giscus-react
```

```jsx:/src/components/Comment.jsx
'use client';

import { Giscus } from 'giscus-react';

export default function Comment() {
  return (
    <div className="mt-12">
      <Giscus
        repo="your-username/your-repo"
        repoId="R_kgDOJqXvFQ"
        category="Announcements"
        categoryId="DIC_kwDOJqXvFc4CbA9U"
        mapping="pathname"
        reactionsEnabled="1"
        emitMetadata="0"
        theme="light"
        lang="zh-CN"
      />
    </div>
  );
}
```

配置步骤：

1. 访问 [giscus 官网](https://giscus.app/zh-CN) 获取配置参数
2. 替换 repo 和 repoId 为您的 GitHub 仓库信息
3. 在需要评论区的 MDX 文件中引入 Comment 组件
4. 确保 GitHub 仓库已启用 Discussions 功能（Settings → Features → Discussions）