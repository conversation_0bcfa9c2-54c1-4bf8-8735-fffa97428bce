/**
 * 首页相关常量配置
 * 统一管理首页的各种配置数据
 */

import {
  Code,
  Palette,
  Lightbulb,
  Pencil,
  BookOpen,
  FileText,
  Compass,
  Github,
} from 'lucide-react';

// 根据时间段的问候语数据
export type TimeOfDay = 'morning' | 'afternoon' | 'evening' | 'night';

export const GREETINGS_BY_TIME: Record<TimeOfDay, string[]> = {
  morning: ['早安', '早上好', '早'],
  afternoon: ['午安', '下午好'],
  evening: ['晚安', '晚上好'],
  night: ['夜深了', '该休息了'],
};

// 推荐标签数据
export const RECOMMENDATION_TAGS = {
  initial: [
    { icon: Code, text: '网页开发', href: '/docs/web-development' },
    { icon: Palette, text: '深入研究', href: '/docs/research' },
    { icon: Lightbulb, text: '项目模式', href: '/docs/project-patterns' },
    { icon: Pencil, text: '图像生成', href: '/docs/image-generation' },
  ],
  more: [
    { icon: BookOpen, text: '文档中心', href: '/docs' },
    { icon: FileText, text: '博客文章', href: '/blog' },
    { icon: Compass, text: '网址导航', href: '/navigation' },
    { icon: Github, text: 'GitHub', href: 'https://github.com/iflux-art/web' },
  ],
} as const;

// 背景样式类型
export const BACKGROUND_STYLES = ['wave'] as const;

// 背景样式类型
export type BackgroundStyle = (typeof BACKGROUND_STYLES)[number];
