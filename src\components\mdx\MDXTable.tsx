'use client';

import { type HTMLAttributes, forwardRef } from 'react';
import { cn } from '@/utils';

/**
 * MDX Table 组件属性
 */
interface MDXTableProps extends HTMLAttributes<HTMLDivElement> {
  /** 是否使用条纹样式 */
  striped?: boolean;
  /** 是否使用紧凑模式 */
  compact?: boolean;
  /** 是否启用悬停高亮 */
  hover?: boolean;
  /** 是否启用边框 */
  bordered?: boolean;
}

/**
 * MDX Table 组件 - 表格容器
 * 提供响应式表格容器，支持水平滚动和主题适配
 */
export const MDXTable = forwardRef<HTMLDivElement, MDXTableProps>(
  ({ className, striped, compact, hover, bordered, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          // 基础样式
          'my-6 w-full overflow-hidden',
          'rounded-lg border border-border',
          'bg-card text-card-foreground',
          // 阴影效果
          'shadow-sm dark:shadow-md',
          // 移动端水平滚动
          'overflow-x-auto',
          // 滚动条样式优化
          'scrollbar-thin scrollbar-track-transparent scrollbar-thumb-border/50',
          'hover:scrollbar-thumb-border/80',
          // 焦点样式
          'focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2',
          className
        )}
        {...props}
      />
    );
  }
);

MDXTable.displayName = 'MDXTable';

/**
 * MDX TableContent 组件属性
 */
interface MDXTableContentProps extends HTMLAttributes<HTMLTableElement> {
  /** 是否使用条纹样式 */
  striped?: boolean;
  /** 是否使用紧凑模式 */
  compact?: boolean;
  /** 是否启用悬停高亮 */
  hover?: boolean;
  /** 是否启用边框 */
  bordered?: boolean;
}

/**
 * MDX TableContent 组件 - 实际表格内容
 * 提供完整的表格样式和交互功能
 */
export const MDXTableContent = forwardRef<HTMLTableElement, MDXTableContentProps>(
  (
    { className, striped = true, compact = false, hover = true, bordered = false, ...props },
    ref
  ) => {
    return (
      <table
        ref={ref}
        className={cn(
          // 基础样式
          'w-full border-collapse',
          'text-sm leading-relaxed',
          // 字体优化
          'font-normal',
          // 条纹样式
          striped &&
            '[&_tbody_tr:nth-child(odd)]:bg-muted/30 dark:[&_tbody_tr:nth-child(odd)]:bg-muted/20',
          // 悬停样式
          hover && [
            '[&_tbody_tr:hover]:bg-muted/50 dark:[&_tbody_tr:hover]:bg-muted/30',
            '[&_tbody_tr]:transition-colors [&_tbody_tr]:duration-200',
          ],
          // 表头样式
          '[&_thead]:bg-muted/50 dark:[&_thead]:bg-muted/30',
          '[&_thead_th]:border-b [&_thead_th]:border-border',
          '[&_thead_th]:px-4 [&_thead_th]:py-3',
          '[&_thead_th]:text-left [&_thead_th]:font-semibold',
          '[&_thead_th]:text-foreground',
          '[&_thead_th]:whitespace-nowrap',
          // 数据单元格样式
          '[&_tbody_td]:border-b [&_tbody_td]:border-border/50',
          '[&_tbody_td]:px-4',
          compact ? '[&_tbody_td]:py-2' : '[&_tbody_td]:py-3',
          '[&_tbody_td]:text-foreground',
          // 边框样式
          bordered && '[&_td]:border-r [&_td]:border-border/30 [&_td:last-child]:border-r-0',
          bordered && '[&_th]:border-r [&_th]:border-border/30 [&_th:last-child]:border-r-0',
          // 最后一行去除底部边框
          '[&_tbody_tr:last-child_td]:border-b-0',
          // 圆角处理
          '[&_thead_th:first-child]:rounded-tl-lg',
          '[&_thead_th:last-child]:rounded-tr-lg',
          // 响应式优化
          'min-w-full',
          className
        )}
        {...props}
      />
    );
  }
);

MDXTableContent.displayName = 'MDXTableContent';

/**
 * MDX Table 组合组件
 * 提供完整的表格组件映射，用于 MDX 渲染
 */
export const MDXTableComponents = {
  // 表格容器 - 自动包装容器和内容
  table: forwardRef<HTMLTableElement, HTMLAttributes<HTMLTableElement>>(
    ({ className, ...props }, ref) => (
      <MDXTable>
        <MDXTableContent ref={ref} className={className} {...props} />
      </MDXTable>
    )
  ),

  // 表头组件
  thead: forwardRef<HTMLTableSectionElement, HTMLAttributes<HTMLTableSectionElement>>(
    ({ className, ...props }, ref) => (
      <thead
        ref={ref}
        className={cn('bg-muted/50 dark:bg-muted/30', '[&_tr]:border-0', className)}
        {...props}
      />
    )
  ),

  // 表格主体
  tbody: forwardRef<HTMLTableSectionElement, HTMLAttributes<HTMLTableSectionElement>>(
    ({ className, ...props }, ref) => (
      <tbody ref={ref} className={cn('divide-y divide-border/50', className)} {...props} />
    )
  ),

  // 表格行
  tr: forwardRef<HTMLTableRowElement, HTMLAttributes<HTMLTableRowElement>>(
    ({ className, ...props }, ref) => (
      <tr
        ref={ref}
        className={cn(
          'transition-colors duration-200',
          'hover:bg-muted/50 dark:hover:bg-muted/30',
          className
        )}
        {...props}
      />
    )
  ),

  // 表头单元格
  th: forwardRef<HTMLTableCellElement, HTMLAttributes<HTMLTableCellElement>>(
    ({ className, ...props }, ref) => (
      <th
        ref={ref}
        className={cn(
          'px-4 py-3 text-left font-semibold',
          'text-foreground whitespace-nowrap',
          'border-b border-border',
          className
        )}
        {...props}
      />
    )
  ),

  // 数据单元格
  td: forwardRef<HTMLTableCellElement, HTMLAttributes<HTMLTableCellElement>>(
    ({ className, ...props }, ref) => (
      <td
        ref={ref}
        className={cn('px-4 py-3 text-foreground', 'border-b border-border/50', className)}
        {...props}
      />
    )
  ),
};

// 为组件添加 displayName
MDXTableComponents.table.displayName = 'MDXTable';
MDXTableComponents.thead.displayName = 'MDXThead';
MDXTableComponents.tbody.displayName = 'MDXTbody';
MDXTableComponents.tr.displayName = 'MDXTr';
MDXTableComponents.th.displayName = 'MDXTh';
MDXTableComponents.td.displayName = 'MDXTd';
