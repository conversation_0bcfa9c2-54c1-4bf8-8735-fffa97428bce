<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>离线模式 - iFluxArt · 斐流艺创</title>
  <style>
    :root {
      --background: #ffffff;
      --foreground: #000000;
      --primary: #0070f3;
      --border: #e2e8f0;
      --muted: #f1f5f9;
      --muted-foreground: #64748b;
    }
    
    @media (prefers-color-scheme: dark) {
      :root {
        --background: #000000;
        --foreground: #ffffff;
        --primary: #3b82f6;
        --border: #1e293b;
        --muted: #0f172a;
        --muted-foreground: #94a3b8;
      }
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      background-color: var(--background);
      color: var(--foreground);
      display: flex;
      flex-direction: column;
      min-height: 100vh;
      padding: 2rem;
    }
    
    .container {
      max-width: 640px;
      margin: 0 auto;
      width: 100%;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      text-align: center;
    }
    
    h1 {
      font-size: 2rem;
      margin-bottom: 1rem;
    }
    
    p {
      margin-bottom: 1.5rem;
      color: var(--muted-foreground);
      line-height: 1.6;
    }
    
    .button {
      display: inline-block;
      background-color: var(--primary);
      color: white;
      padding: 0.75rem 1.5rem;
      border-radius: 0.375rem;
      text-decoration: none;
      font-weight: 500;
      transition: opacity 0.2s;
    }
    
    .button:hover {
      opacity: 0.9;
    }
    
    .icon {
      font-size: 4rem;
      margin-bottom: 2rem;
      color: var(--primary);
    }
    
    .footer {
      margin-top: 2rem;
      font-size: 0.875rem;
      color: var(--muted-foreground);
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="icon">📶</div>
    <h1>您当前处于离线模式</h1>
    <p>
      看起来您的网络连接已断开。<br>
      我们已经为您缓存了一些内容，但无法加载新内容。<br>
      请检查您的网络连接并重试。
    </p>
    <a href="/" class="button">刷新页面</a>
    <div class="footer">
      &copy; 2023 iFluxArt · 斐流艺创
    </div>
  </div>
  
  <script>
    // 检测网络状态
    function checkNetworkStatus() {
      if (navigator.onLine) {
        document.querySelector('.button').textContent = '返回首页';
      } else {
        document.querySelector('.button').textContent = '刷新页面';
      }
    }
    
    // 初始检测
    checkNetworkStatus();
    
    // 监听网络状态变化
    window.addEventListener('online', checkNetworkStatus);
    window.addEventListener('offline', checkNetworkStatus);
    
    // 点击按钮时刷新页面
    document.querySelector('.button').addEventListener('click', function(e) {
      e.preventDefault();
      if (navigator.onLine) {
        window.location.href = '/';
      } else {
        window.location.reload();
      }
    });
  </script>
</body>
</html>
