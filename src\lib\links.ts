/**
 * 链接管理工具函数
 * 提供链接数据的读取、写入和管理功能
 */

import fs from 'fs';
import path from 'path';
import type { LinksCategory, LinksItem, LinksData } from '@/types';
import { links } from '@/config/links';

// ==================== 文件路径配置 ====================

const CATEGORIES_FILE_PATH = path.join(process.cwd(), 'data', 'links', 'categories.json');
const ITEMS_FILE_PATH = path.join(process.cwd(), 'data', 'links', 'items.json');

// ==================== 内部工具函数 ====================

/**
 * 确保数据目录存在
 */
function ensureDataDirectory() {
  const dataDir = path.dirname(CATEGORIES_FILE_PATH);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
}

/**
 * 读取项目数据
 * @internal 内部函数，用于底层数据读取操作
 */
function readItems(): LinksItem[] {
  if (!fs.existsSync(ITEMS_FILE_PATH)) {
    fs.writeFileSync(ITEMS_FILE_PATH, JSON.stringify([], null, 2), 'utf-8');
    return [];
  }

  try {
    const content = fs.readFileSync(ITEMS_FILE_PATH, 'utf-8');
    return JSON.parse(content);
  } catch (error) {
    console.error('Error reading items data:', error);
    throw new Error('Failed to read items data');
  }
}

/**
 * 写入项目数据
 * @internal 内部函数，用于底层数据写入操作
 * @param items - 要写入的导航项数组
 */
function writeItems(items: LinksItem[]): void {
  try {
    fs.writeFileSync(ITEMS_FILE_PATH, JSON.stringify(items, null, 2), 'utf-8');
  } catch (error) {
    console.error('Error writing items data:', error);
    throw new Error('Failed to write items data');
  }
}

// ==================== 公共 API 函数 ====================

/**
 * 读取完整导航数据
 */
export function readLinksData(): LinksData {
  return {
    categories: links.categories.map(cat => ({
      ...cat,
      title: cat.name,
      description: cat.description,
      count: links.items.filter(item => item.category === cat.id).length,
    })),
    items: links.items.map(item => ({
      ...item,
      createdAt: item.createdAt,
      updatedAt: item.updatedAt,
    })),
  };
}

/**
 * 写入导航数据
 */
export function writeLinksData(data: LinksData): void {
  ensureDataDirectory();

  try {
    fs.writeFileSync(CATEGORIES_FILE_PATH, JSON.stringify(data.categories, null, 2), 'utf-8');
    fs.writeFileSync(ITEMS_FILE_PATH, JSON.stringify(data.items, null, 2), 'utf-8');
  } catch (error) {
    console.error('Error writing links data:', error);
    throw new Error('Failed to write links data');
  }
}

/**
 * 添加新的导航项
 * @param item - 要添加的导航项
 * @returns 添加后的导航项（包含生成的ID）
 */
export function addLinksItem(item: Omit<LinksItem, 'id' | 'createdAt' | 'updatedAt'>): LinksItem {
  const items = readItems();
  const now = new Date().toISOString();

  const newItem: LinksItem = {
    ...item,
    id: generateId(),
    createdAt: now,
    updatedAt: now,
  };

  items.push(newItem);
  writeItems(items);

  return newItem;
}

/**
 * 更新导航项
 * @param id - 导航项ID
 * @param updates - 要更新的字段
 * @returns 更新后的导航项，如果未找到则返回null
 */
export function updateLinksItem(
  id: string,
  updates: Partial<Omit<LinksItem, 'id' | 'createdAt'>>
): LinksItem | null {
  const items = readItems();
  const index = items.findIndex(item => item.id === id);

  if (index === -1) {
    return null;
  }

  const updatedItem: LinksItem = {
    ...items[index],
    ...updates,
    updatedAt: new Date().toISOString(),
  };

  items[index] = updatedItem;
  writeItems(items);

  return updatedItem;
}

/**
 * 删除导航项
 * @param id - 要删除的导航项ID
 * @returns 是否删除成功
 */
export function deleteLinksItem(id: string): boolean {
  const items = readItems();
  const index = items.findIndex(item => item.id === id);

  if (index === -1) {
    return false;
  }

  items.splice(index, 1);
  writeItems(items);

  return true;
}

/**
 * 获取单个导航项
 * @param id - 导航项ID
 * @returns 导航项，如果未找到则返回null
 */
export function getLinksItem(id: string): LinksItem | null {
  const items = readItems();
  return items.find(item => item.id === id) || null;
}

/**
 * 获取所有导航项
 * @returns 所有导航项数组
 */
export function getAllLinksItems(): LinksItem[] {
  return readItems();
}

/**
 * 获取分类列表
 * @returns 分类数组
 */
export function getCategories(): LinksCategory[] {
  return links.categories.map(cat => ({
    ...cat,
    title: cat.name,
    description: cat.description,
    count: links.items.filter(item => item.category === cat.id).length,
  }));
}

/**
 * 根据分类获取导航项
 * @param categoryId - 分类ID
 * @returns 该分类下的导航项数组
 */
export function getLinksItemsByCategory(categoryId: string): LinksItem[] {
  const items = readItems();
  return items.filter(item => item.category === categoryId);
}

/**
 * 搜索导航项
 * @param query - 搜索关键词
 * @returns 匹配的导航项数组
 */
export function searchLinksItems(query: string): LinksItem[] {
  if (!query.trim()) {
    return getAllLinksItems();
  }

  const items = readItems();
  const lowerQuery = query.toLowerCase();

  return items.filter(
    item =>
      item.title.toLowerCase().includes(lowerQuery) ||
      item.description.toLowerCase().includes(lowerQuery) ||
      item.tags.some(tag => tag.toLowerCase().includes(lowerQuery)) ||
      item.url.toLowerCase().includes(lowerQuery)
  );
}

// ==================== 工具函数 ====================

/**
 * 生成唯一ID
 * @returns 唯一标识符
 */
function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

/**
 * 验证导航项数据
 * @param item - 要验证的导航项
 * @returns 验证结果
 */
export function validateLinksItem(item: Partial<LinksItem>): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!item.title?.trim()) {
    errors.push('标题不能为空');
  }

  if (!item.url?.trim()) {
    errors.push('URL不能为空');
  } else {
    try {
      new URL(item.url);
    } catch {
      errors.push('URL格式不正确');
    }
  }

  if (!item.category) {
    errors.push('必须选择分类');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * 批量操作导航项
 * @param ids - 导航项ID数组
 * @param action - 操作类型
 * @returns 操作结果
 */
export function batchLinksItems(
  ids: string[],
  action: 'delete' | 'feature' | 'unfeature'
): { success: number; failed: number } {
  let success = 0;
  let failed = 0;

  for (const id of ids) {
    try {
      switch (action) {
        case 'delete':
          if (deleteLinksItem(id)) {
            success++;
          } else {
            failed++;
          }
          break;
        case 'feature':
          if (updateLinksItem(id, { featured: true })) {
            success++;
          } else {
            failed++;
          }
          break;
        case 'unfeature':
          if (updateLinksItem(id, { featured: false })) {
            success++;
          } else {
            failed++;
          }
          break;
        default:
          failed++;
      }
    } catch {
      failed++;
    }
  }

  return { success, failed };
}
