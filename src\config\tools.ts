/**
 * 工具相关配置
 * 统一管理工具分类、工具列表和相关配置
 */

import { Wrench, PenTool, Code, Palette, FileText, Smile } from 'lucide-react';
import type { LucideIcon } from 'lucide-react';

// ==================== 类型定义 ====================

/**
 * 工具分类接口
 */
export interface ToolCategory {
  id: string;
  name: string;
  icon: LucideIcon;
  description?: string;
  color?: string;
}

/**
 * 工具接口
 */
export interface Tool {
  id: string;
  name: string;
  description: string;
  category: string;
  path: string;
  tags: string[];
  isInternal: boolean;
  featured?: boolean;
  difficulty?: 'easy' | 'medium' | 'hard';
  status?: 'stable' | 'beta' | 'experimental';
}

// ==================== 工具分类配置 ====================

/**
 * 工具分类配置
 */
export const TOOL_CATEGORIES: ToolCategory[] = [
  {
    id: 'development',
    name: '开发工具',
    icon: Code,
    description: '代码处理、格式化、调试等开发相关工具',
    color: 'blue',
  },
  {
    id: 'design',
    name: '设计工具',
    icon: Palette,
    description: '颜色选择、图像处理、设计辅助工具',
    color: 'purple',
  },
  {
    id: 'text',
    name: '文本工具',
    icon: PenTool,
    description: '文本处理、格式转换、编辑工具',
    color: 'green',
  },
  {
    id: 'conversion',
    name: '转换工具',
    icon: FileText,
    description: '格式转换、编码解码、数据转换工具',
    color: 'orange',
  },
  {
    id: 'utility',
    name: '实用工具',
    icon: Wrench,
    description: '日常实用、计算、生成等工具',
    color: 'gray',
  },
  {
    id: 'entertainment',
    name: '娱乐工具',
    icon: Smile,
    description: '趣味生成、游戏、娱乐相关工具',
    color: 'pink',
  },
];

// ==================== 工具列表配置 ====================

/**
 * 工具列表配置
 */
export const TOOLS: Tool[] = [
  {
    id: 'code-toolkit',
    name: '代码工具箱',
    description: '一站式代码处理工具，支持代码格式化、压缩、JSON/CSV转换、HTML编解码等功能',
    category: 'development',
    path: '/tools/code-toolkit',
    tags: ['代码', '格式化', '压缩', 'JSON', 'CSV', 'HTML'],
    isInternal: true,
    featured: true,
    difficulty: 'easy',
    status: 'stable',
  },
  {
    id: 'regex-tester',
    name: '正则表达式工具',
    description: '强大的正则表达式测试工具，支持实时匹配、模式验证、语法高亮、常用模式库',
    category: 'development',
    path: '/tools/regex-tester',
    tags: ['正则', '测试', '匹配', '验证', '模式'],
    isInternal: true,
    featured: true,
    difficulty: 'medium',
    status: 'stable',
  },
  {
    id: 'color-picker',
    name: '颜色选择器',
    description: '专业的颜色选择工具，支持HEX、RGB、HSL格式转换，提供配色方案和调色板',
    category: 'design',
    path: '/tools/color-picker',
    tags: ['颜色', '配色', '调色板', 'HEX', 'RGB'],
    isInternal: true,
    featured: true,
    difficulty: 'easy',
    status: 'stable',
  },
  {
    id: 'base64-encoder',
    name: 'Base64 编解码',
    description: '快速的Base64编码和解码工具，支持文本和文件处理，提供实时转换功能',
    category: 'conversion',
    path: '/tools/base64-encoder',
    tags: ['Base64', '编码', '解码', '转换'],
    isInternal: true,
    difficulty: 'easy',
    status: 'stable',
  },
  {
    id: 'markdown-editor',
    name: 'Markdown 编辑器',
    description: '功能丰富的Markdown编辑器，支持实时预览、语法高亮、导出功能',
    category: 'text',
    path: '/tools/markdown-editor',
    tags: ['Markdown', '编辑器', '预览', '导出'],
    isInternal: true,
    featured: true,
    difficulty: 'easy',
    status: 'stable',
  },
  {
    id: 'text-counter',
    name: '文本统计工具',
    description: '全面的文本分析工具，统计字符数、单词数、行数，支持多种语言',
    category: 'text',
    path: '/tools/text-counter',
    tags: ['文本', '统计', '字符', '单词', '分析'],
    isInternal: true,
    difficulty: 'easy',
    status: 'stable',
  },
  {
    id: 'timestamp-converter',
    name: '时间戳转换器',
    description: '时间戳与日期时间的双向转换工具，支持多种时间格式和时区',
    category: 'conversion',
    path: '/tools/timestamp-converter',
    tags: ['时间戳', '日期', '转换', '时区'],
    isInternal: true,
    difficulty: 'easy',
    status: 'stable',
  },
  {
    id: 'random-generator',
    name: '随机生成器',
    description: '多功能随机生成工具，支持密码、UUID、随机数、随机字符串生成',
    category: 'utility',
    path: '/tools/random-generator',
    tags: ['随机', '密码', 'UUID', '生成器'],
    isInternal: true,
    difficulty: 'easy',
    status: 'stable',
  },
  {
    id: 'css-toolkit',
    name: 'CSS 工具箱',
    description: 'CSS相关工具集合，包括CSS格式化、压缩、选择器生成等功能',
    category: 'development',
    path: '/tools/css-toolkit',
    tags: ['CSS', '格式化', '压缩', '选择器'],
    isInternal: true,
    difficulty: 'medium',
    status: 'stable',
  },
  {
    id: 'unit-converter',
    name: '单位转换器',
    description: '全面的单位转换工具，支持长度、重量、温度、面积等多种单位转换',
    category: 'utility',
    path: '/tools/unit-converter',
    tags: ['单位', '转换', '长度', '重量', '温度'],
    isInternal: true,
    difficulty: 'easy',
    status: 'stable',
  },
  {
    id: 'calculator',
    name: '科学计算器',
    description: '功能强大的科学计算器，支持基础运算、科学函数、进制转换',
    category: 'utility',
    path: '/tools/calculator',
    tags: ['计算器', '科学', '函数', '进制'],
    isInternal: true,
    difficulty: 'easy',
    status: 'stable',
  },
  {
    id: 'symbol-collection',
    name: '符号大全',
    description: '丰富的特殊符号集合，包括数学符号、箭头、表情等，一键复制',
    category: 'text',
    path: '/tools/symbol-collection',
    tags: ['符号', '特殊字符', '数学', '表情'],
    isInternal: true,
    difficulty: 'easy',
    status: 'stable',
  },
  {
    id: 'content-creator',
    name: '内容创作助手',
    description: '内容创作辅助工具，提供文案生成、标题优化、关键词分析等功能',
    category: 'text',
    path: '/tools/content-creator',
    tags: ['内容', '创作', '文案', '标题', '关键词'],
    isInternal: true,
    featured: true,
    difficulty: 'medium',
    status: 'beta',
  },
  {
    id: 'nonsense-generator',
    name: '无意义文本生成器',
    description: '生成各种无意义文本，用于测试、占位、演示等场景',
    category: 'entertainment',
    path: '/tools/nonsense-generator',
    tags: ['无意义', '文本', '生成', '测试', '占位'],
    isInternal: true,
    difficulty: 'easy',
    status: 'stable',
  },
];

// ==================== 工具配置常量 ====================

/**
 * 工具页面配置
 */
export const TOOLS_CONFIG = {
  /** 每页显示的工具数量 */
  ITEMS_PER_PAGE: 12,
  /** 默认分类 */
  DEFAULT_CATEGORY: 'all',
  /** 是否启用搜索 */
  ENABLE_SEARCH: true,
  /** 是否启用分类过滤 */
  ENABLE_CATEGORY_FILTER: true,
  /** 是否启用标签过滤 */
  ENABLE_TAG_FILTER: true,
  /** 是否显示工具状态 */
  SHOW_STATUS: true,
  /** 是否显示难度等级 */
  SHOW_DIFFICULTY: true,
} as const;

/**
 * 工具状态配置
 */
export const TOOL_STATUS_CONFIG = {
  stable: {
    label: '稳定',
    color: 'green',
    description: '功能完善，可放心使用',
  },
  beta: {
    label: '测试',
    color: 'yellow',
    description: '功能基本完善，可能存在小问题',
  },
  experimental: {
    label: '实验',
    color: 'red',
    description: '实验性功能，可能不稳定',
  },
} as const;

/**
 * 工具难度配置
 */
export const TOOL_DIFFICULTY_CONFIG = {
  easy: {
    label: '简单',
    color: 'green',
    description: '操作简单，适合所有用户',
  },
  medium: {
    label: '中等',
    color: 'yellow',
    description: '需要一定的专业知识',
  },
  hard: {
    label: '困难',
    color: 'red',
    description: '需要专业知识和经验',
  },
} as const;

// ==================== 工具搜索数据 ====================

/**
 * 搜索用的简化工具数据
 */
export const SEARCH_TOOLS = TOOLS.map(tool => ({
  id: tool.id,
  name: tool.name,
  description: tool.description,
  path: tool.path,
  tags: tool.tags,
}));

// ==================== 工具分类映射 ====================

/**
 * 根据分类ID获取分类信息
 */
export function getToolCategory(categoryId: string): ToolCategory | undefined {
  return TOOL_CATEGORIES.find(category => category.id === categoryId);
}

/**
 * 根据分类获取工具列表
 */
export function getToolsByCategory(categoryId: string): Tool[] {
  if (categoryId === 'all') {
    return TOOLS;
  }
  return TOOLS.filter(tool => tool.category === categoryId);
}

/**
 * 获取特色工具列表
 */
export function getFeaturedTools(): Tool[] {
  return TOOLS.filter(tool => tool.featured);
}

/**
 * 根据标签搜索工具
 */
export function searchToolsByTag(tag: string): Tool[] {
  return TOOLS.filter(tool => tool.tags.some(t => t.toLowerCase().includes(tag.toLowerCase())));
}
