/**
 * 网站解析工具函数
 * 提供网站元数据解析和URL处理功能
 */

import { WebsiteMetadata } from '@/types';
import { PARSER_CONFIG } from '@/config/links';

// ==================== 网站元数据解析 ====================

/**
 * 解析网站元数据
 */
export async function parseWebsiteMetadata(url: string): Promise<WebsiteMetadata> {
  try {
    // 确保 URL 格式正确
    const normalizedUrl = normalizeUrl(url);

    // 使用代理服务解析网站信息
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), PARSER_CONFIG.TIMEOUT);

    const response = await fetch(`/api/parse-website?url=${encodeURIComponent(normalizedUrl)}`, {
      signal: controller.signal,
      headers: {
        'User-Agent': PARSER_CONFIG.USER_AGENT,
      },
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error('Failed to parse website');
    }

    const metadata = await response.json();
    return metadata;
  } catch (error) {
    console.error('Error parsing website:', error);

    // 返回基础信息
    return {
      title: extractDomainName(url),
      description: '',
      icon: '',
    };
  }
}

/**
 * 标准化 URL
 */
export function normalizeUrl(url: string): string {
  // 如果没有协议，添加 https://
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    url = 'https://' + url;
  }

  try {
    const urlObj = new URL(url);
    return urlObj.href;
  } catch {
    throw new Error('Invalid URL format');
  }
}

/**
 * 从 URL 提取域名作为默认标题
 */
export function extractDomainName(url: string): string {
  try {
    const urlObj = new URL(normalizeUrl(url));
    let domain = urlObj.hostname;

    // 移除 www. 前缀
    if (domain.startsWith('www.')) {
      domain = domain.substring(4);
    }

    // 首字母大写
    return domain.charAt(0).toUpperCase() + domain.slice(1);
  } catch {
    return 'Unknown Site';
  }
}

/**
 * 验证 URL 格式
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(normalizeUrl(url));
    return true;
  } catch {
    return false;
  }
}

/**
 * 验证图片 URL
 */
export function isValidImageUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname.toLowerCase();

    return PARSER_CONFIG.SUPPORTED_IMAGE_FORMATS.some(format => pathname.endsWith(`.${format}`));
  } catch {
    return false;
  }
}

/**
 * 获取网站图标
 */
export async function getWebsiteIcon(url: string): Promise<string> {
  try {
    const urlObj = new URL(normalizeUrl(url));
    const baseUrl = `${urlObj.protocol}//${urlObj.hostname}`;

    // 尝试常见的图标路径
    const iconPaths = [
      '/favicon.ico',
      '/favicon.png',
      '/apple-touch-icon.png',
      '/apple-touch-icon-precomposed.png',
    ];

    for (const path of iconPaths) {
      const iconUrl = baseUrl + path;

      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        const response = await fetch(iconUrl, {
          method: 'HEAD',
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          return iconUrl;
        }
      } catch {
        // 继续尝试下一个路径
      }
    }

    // 如果都失败了，返回默认图标
    return '';
  } catch {
    return '';
  }
}

/**
 * 解析页面标题
 */
export function parsePageTitle(html: string): string {
  const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
  if (titleMatch && titleMatch[1]) {
    return titleMatch[1].trim();
  }

  // 尝试 og:title
  const ogTitleMatch = html.match(
    /<meta[^>]*property=['""]og:title['""][^>]*content=['""]([^'"]*)['""][^>]*>/i
  );
  if (ogTitleMatch && ogTitleMatch[1]) {
    return ogTitleMatch[1].trim();
  }

  return '';
}

/**
 * 解析页面描述
 */
export function parsePageDescription(html: string): string {
  // 尝试 meta description
  const descMatch = html.match(
    /<meta[^>]*name=['""]description['""][^>]*content=['""]([^'"]*)['""][^>]*>/i
  );
  if (descMatch && descMatch[1]) {
    return descMatch[1].trim();
  }

  // 尝试 og:description
  const ogDescMatch = html.match(
    /<meta[^>]*property=['""]og:description['""][^>]*content=['""]([^'"]*)['""][^>]*>/i
  );
  if (ogDescMatch && ogDescMatch[1]) {
    return ogDescMatch[1].trim();
  }

  return '';
}

/**
 * 解析页面图标
 */
export function parsePageIcon(html: string, baseUrl: string): string {
  // 尝试各种图标标签
  const iconPatterns = [
    /<link[^>]*rel=['""]icon['""][^>]*href=['""]([^'"]*)['""][^>]*>/i,
    /<link[^>]*rel=['""]shortcut icon['""][^>]*href=['""]([^'"]*)['""][^>]*>/i,
    /<link[^>]*rel=['""]apple-touch-icon['""][^>]*href=['""]([^'"]*)['""][^>]*>/i,
    /<meta[^>]*property=['""]og:image['""][^>]*content=['""]([^'"]*)['""][^>]*>/i,
  ];

  for (const pattern of iconPatterns) {
    const match = html.match(pattern);
    if (match && match[1]) {
      let iconUrl = match[1].trim();

      // 处理相对路径
      if (iconUrl.startsWith('/')) {
        iconUrl = baseUrl + iconUrl;
      } else if (!iconUrl.startsWith('http')) {
        iconUrl = baseUrl + '/' + iconUrl;
      }

      return iconUrl;
    }
  }

  return '';
}

/**
 * 获取完整的网站元数据
 */
export async function getFullWebsiteMetadata(url: string): Promise<WebsiteMetadata> {
  try {
    const normalizedUrl = normalizeUrl(url);
    const urlObj = new URL(normalizedUrl);
    const baseUrl = `${urlObj.protocol}//${urlObj.hostname}`;

    // 获取页面HTML
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), PARSER_CONFIG.TIMEOUT);

    const response = await fetch(normalizedUrl, {
      signal: controller.signal,
      headers: {
        'User-Agent': PARSER_CONFIG.USER_AGENT,
      },
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    const html = await response.text();

    // 解析元数据
    const title = parsePageTitle(html) || extractDomainName(url);
    const description = parsePageDescription(html);
    const icon = parsePageIcon(html, baseUrl) || (await getWebsiteIcon(url));

    return {
      title,
      description,
      icon,
    };
  } catch (error) {
    console.error('Error getting website metadata:', error);

    return {
      title: extractDomainName(url),
      description: '',
      icon: '',
    };
  }
}

// ==================== URL 工具函数 ====================

/**
 * 检查URL是否可访问
 */
export async function isUrlAccessible(url: string): Promise<boolean> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);

    const response = await fetch(normalizeUrl(url), {
      method: 'HEAD',
      signal: controller.signal,
    });

    clearTimeout(timeoutId);
    return response.ok;
  } catch {
    return false;
  }
}

/**
 * 获取URL的最终重定向地址
 */
export async function getFinalUrl(url: string): Promise<string> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);

    const response = await fetch(normalizeUrl(url), {
      method: 'HEAD',
      redirect: 'follow',
      signal: controller.signal,
    });

    clearTimeout(timeoutId);
    return response.url;
  } catch {
    return url;
  }
}

/**
 * 提取URL参数
 */
export function extractUrlParams(url: string): Record<string, string> {
  try {
    const urlObj = new URL(normalizeUrl(url));
    const params: Record<string, string> = {};

    urlObj.searchParams.forEach((value, key) => {
      params[key] = value;
    });

    return params;
  } catch {
    return {};
  }
}

/**
 * 清理URL（移除跟踪参数等）
 */
export function cleanUrl(url: string): string {
  try {
    const urlObj = new URL(normalizeUrl(url));

    // 常见的跟踪参数
    const trackingParams = [
      'utm_source',
      'utm_medium',
      'utm_campaign',
      'utm_term',
      'utm_content',
      'fbclid',
      'gclid',
      'msclkid',
      'ref',
      'source',
    ];

    trackingParams.forEach(param => {
      urlObj.searchParams.delete(param);
    });

    return urlObj.href;
  } catch {
    return url;
  }
}
