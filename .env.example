# Algolia 配置
NEXT_PUBLIC_ALGOLIA_APP_ID="你的应用ID"
NEXT_PUBLIC_ALGOLIA_SEARCH_KEY="你的搜索API密钥"
NEXT_PUBLIC_ALGOLIA_INDEX_NAME="你的索引名称"
ALGOLIA_ADMIN_KEY="你的管理员API密钥" # 不要在客户端暴露这个密钥

# AI 模型 API 密钥配置
# 配置你想要使用的 AI 模型的 API 密钥

# DeepSeek API 密钥
# 获取地址：https://platform.deepseek.com/
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# 硅基流动 API 密钥 (支持多种模型：Qwen、DeepSeek、Llama等)
# 获取地址：https://siliconflow.cn/
SILICONFLOW_API_KEY=your_siliconflow_api_key_here

# OpenRouter API 密钥 (支持 GPT-4、Claude、Gemini 等)
# 获取地址：https://openrouter.ai/
OPENROUTER_API_KEY=your_openrouter_api_key_here

# GitHub Copilot Token
# 获取地址：https://github.com/settings/tokens
GITHUB_TOKEN=your_github_token_here

# Moonshot AI (Kimi) API 密钥
# 获取地址：https://platform.moonshot.cn/
MOONSHOT_API_KEY=your_moonshot_api_key_here

# 智谱AI GLM API 密钥
# 获取地址：https://open.bigmodel.cn/
ZHIPU_API_KEY=your_zhipu_api_key_here

# 百川智能 API 密钥
# 获取地址：https://platform.baichuan-ai.com/
BAICHUAN_API_KEY=your_baichuan_api_key_here

# MiniMax API 密钥
# 获取地址：https://api.minimax.chat/
MINIMAX_API_KEY=your_minimax_api_key_here

# 网站配置
NEXT_PUBLIC_SITE_URL=http://localhost:3000

# 管理员登录配置
# 默认用户名: ifa2025
# 默认密码: Hogyoku2025

# 其他邮件服务商配置示例：
# 阿里云邮件推送
# EMAIL_SERVER_HOST=smtpdm.aliyun.com
# EMAIL_SERVER_PORT=80
# EMAIL_SERVER_USER=your-username
# EMAIL_SERVER_PASSWORD=your-password

# 腾讯云邮件推送
# EMAIL_SERVER_HOST=smtp.qcloudmail.com
# EMAIL_SERVER_PORT=587
# EMAIL_SERVER_USER=your-username
# EMAIL_SERVER_PASSWORD=your-password
