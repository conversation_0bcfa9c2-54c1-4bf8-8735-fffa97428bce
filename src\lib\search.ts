/**
 * 内容搜索工具函数
 * 提供内容文件扫描、搜索和高亮功能
 */

import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';
import { SEARCH_CONFIG, SEARCH_TYPE_LABELS } from '@/config/search';

// ==================== 类型定义 ====================

export interface ContentItem {
  title: string;
  description: string;
  url: string;
  type: 'doc' | 'blog' | 'tool' | 'web';
  content?: string;
  excerpt?: string;
  tags?: string[];
  date?: string;
  slug: string;
  filePath: string;
  highlights?: {
    title?: string;
    content?: string[];
  };
}

export interface SearchResult extends ContentItem {
  score: number;
}

// ==================== 内容文件扫描 ====================

/**
 * 获取所有内容文件
 */
export function getAllContentFiles(): ContentItem[] {
  const contentDir = path.join(process.cwd(), 'src/content');
  const items: ContentItem[] = [];

  function scanDirectory(dir: string, baseType: 'doc' | 'blog' = 'doc') {
    if (!fs.existsSync(dir)) return;

    const files = fs.readdirSync(dir);

    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        // 递归扫描子目录
        scanDirectory(filePath, baseType);
      } else if (file.endsWith('.mdx') && !file.startsWith('_')) {
        try {
          const fileContent = fs.readFileSync(filePath, 'utf-8');
          const { data: frontmatter, content } = matter(fileContent);

          // 生成URL路径
          const relativePath = path.relative(contentDir, filePath);
          const urlPath = relativePath
            .replace(/\\/g, '/')
            .replace(/\.mdx$/, '')
            .replace(/\/index$/, '');

          // 确定内容类型
          let type: 'doc' | 'blog' | 'tool' | 'web' = 'doc';
          if (relativePath.startsWith('blog')) {
            type = 'blog';
          } else if (relativePath.startsWith('docs')) {
            type = 'doc';
          }

          // 生成摘要（取前200个字符）
          const plainContent = extractPlainText(content);
          const excerpt = generateExcerpt(plainContent, 200);

          items.push({
            title: frontmatter.title || path.basename(file, '.mdx'),
            description: frontmatter.excerpt || excerpt,
            url: `/${urlPath}`,
            type,
            content: plainContent,
            excerpt: frontmatter.excerpt || excerpt,
            tags: frontmatter.tags || [],
            date: frontmatter.date,
            slug: path.basename(file, '.mdx'),
            filePath: relativePath,
          });
        } catch (error) {
          console.error(`Error reading file ${filePath}:`, error);
        }
      }
    }
  }

  // 扫描docs目录
  scanDirectory(path.join(contentDir, 'docs'), 'doc');

  // 扫描blog目录
  scanDirectory(path.join(contentDir, 'blog'), 'blog');

  return items;
}

// ==================== 搜索功能 ====================

/**
 * 搜索内容
 */
export function searchContent(
  query: string, 
  limit: number = SEARCH_CONFIG.MAX_RESULTS
): ContentItem[] {
  if (!query.trim() || query.length < SEARCH_CONFIG.MIN_QUERY_LENGTH) {
    return [];
  }

  const allContent = getAllContentFiles();
  const lowerQuery = query.toLowerCase();
  const results: SearchResult[] = [];

  for (const item of allContent) {
    const searchResult = calculateSearchScore(item, query, lowerQuery);
    
    if (searchResult.score > 0) {
      results.push(searchResult);
    }
  }

  // 按分数排序并返回限定数量的结果
  return results
    .sort((a, b) => b.score - a.score)
    .slice(0, limit)
    .map(({ score: _score, ...item }) => item);
}

/**
 * 计算搜索评分
 */
function calculateSearchScore(item: ContentItem, query: string, lowerQuery: string): SearchResult {
  let score = 0;
  const highlights: { title?: string; content?: string[] } = {};

  // 搜索标题（权重最高）
  const titleMatch = item.title.toLowerCase().includes(lowerQuery);
  if (titleMatch) {
    score += 10;
    highlights.title = highlightText(item.title, query);
  }

  // 精确匹配标题（额外加分）
  if (item.title.toLowerCase() === lowerQuery) {
    score += 20;
  }

  // 搜索描述
  const descMatch = item.description.toLowerCase().includes(lowerQuery);
  if (descMatch) {
    score += 5;
  }

  // 搜索标签
  const tagMatch = item.tags?.some(tag => tag.toLowerCase().includes(lowerQuery));
  if (tagMatch) {
    score += 3;
  }

  // 精确匹配标签（额外加分）
  const exactTagMatch = item.tags?.some(tag => tag.toLowerCase() === lowerQuery);
  if (exactTagMatch) {
    score += 5;
  }

  // 搜索内容
  const contentMatches = findContentMatches(item.content || '', query);
  if (contentMatches.length > 0) {
    score += contentMatches.length;
    highlights.content = contentMatches;
  }

  return {
    ...item,
    score,
    highlights,
  };
}

// ==================== 文本处理工具 ====================

/**
 * 提取纯文本内容
 */
export function extractPlainText(content: string): string {
  return content
    .replace(/#{1,6}\s+/g, '') // 移除标题标记
    .replace(/\*\*([^*]+)\*\*/g, '$1') // 移除粗体标记
    .replace(/\*([^*]+)\*/g, '$1') // 移除斜体标记
    .replace(/`([^`]+)`/g, '$1') // 移除代码标记
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // 移除链接标记
    .replace(/\n+/g, ' ') // 替换换行为空格
    .trim();
}

/**
 * 生成摘要
 */
export function generateExcerpt(text: string, maxLength: number = 200): string {
  if (text.length <= maxLength) {
    return text;
  }
  
  // 尝试在句号处截断
  const sentences = text.split('。');
  let excerpt = '';
  
  for (const sentence of sentences) {
    if ((excerpt + sentence + '。').length <= maxLength) {
      excerpt += sentence + '。';
    } else {
      break;
    }
  }
  
  // 如果没有找到合适的句号截断点，直接截断
  if (!excerpt) {
    excerpt = text.substring(0, maxLength) + '...';
  }
  
  return excerpt;
}

/**
 * 高亮文本
 */
export function highlightText(text: string, query: string): string {
  const regex = new RegExp(`(${escapeRegExp(query)})`, 'gi');
  return text.replace(regex, `<mark class="${SEARCH_CONFIG.HIGHLIGHT_CLASS}">$1</mark>`);
}

/**
 * 查找内容匹配项
 */
export function findContentMatches(
  content: string, 
  query: string, 
  maxMatches: number = 3,
  contextLength: number = 50
): string[] {
  const lowerContent = content.toLowerCase();
  const lowerQuery = query.toLowerCase();
  const matches: string[] = [];

  let startIndex = 0;

  while (matches.length < maxMatches) {
    const index = lowerContent.indexOf(lowerQuery, startIndex);
    if (index === -1) break;

    // 获取匹配周围的上下文
    const start = Math.max(0, index - contextLength);
    const end = Math.min(content.length, index + query.length + contextLength);
    let context = content.substring(start, end);

    // 如果不是从开头开始，添加省略号
    if (start > 0) context = '...' + context;
    if (end < content.length) context = context + '...';

    // 高亮匹配的文本
    const highlightedContext = highlightText(context, query);
    matches.push(highlightedContext);

    startIndex = index + query.length;
  }

  return matches;
}

/**
 * 转义正则表达式特殊字符
 */
export function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// ==================== 搜索历史管理 ====================

/**
 * 保存搜索历史
 */
export function saveSearchHistory(query: string): void {
  if (typeof window === 'undefined') return;
  
  try {
    const history = getSearchHistory();
    const newHistory = [query, ...history.filter(item => item !== query)]
      .slice(0, SEARCH_CONFIG.MAX_HISTORY_ITEMS);
    
    localStorage.setItem('search-history', JSON.stringify(newHistory));
  } catch (error) {
    console.error('Failed to save search history:', error);
  }
}

/**
 * 获取搜索历史
 */
export function getSearchHistory(): string[] {
  if (typeof window === 'undefined') return [];
  
  try {
    const history = localStorage.getItem('search-history');
    return history ? JSON.parse(history) : [];
  } catch (error) {
    console.error('Failed to get search history:', error);
    return [];
  }
}

/**
 * 清除搜索历史
 */
export function clearSearchHistory(): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.removeItem('search-history');
  } catch (error) {
    console.error('Failed to clear search history:', error);
  }
}
