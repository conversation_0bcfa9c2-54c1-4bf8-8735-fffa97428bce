/**
 * MDX 组件统一导出
 * 提供所有 MDX 相关组件和配置的统一入口
 *
 * 注意：组件文件名使用 kebab-case，但导出的组件名保持 PascalCase
 */

// ==================== 核心组件导出 ====================
export { MDXRenderer } from './MDXRenderer';
export { MDXProvider } from './MDXProvider';

// ==================== 配置和类型导出 ====================
export {
  MDXComponentsMapping as MDXComponents,
  MDXBaseOptions,
  MDXStyles,
  useMDXComponents,
  type MDXOptions,
  type MDXComponents as MDXComponentsType,
} from '@/config/mdx';

// ==================== 基础组件导出 ====================
// 使用 kebab-case 文件名，但保持 PascalCase 组件名
export { MDXImage, type MDXImageProps } from './MDXImage';
export { MDXLink, type MDXLinkProps } from './MDXLink';
export { MDXCodeBlock, type MDXCodeBlockProps } from './mdx-code-block';
export { MDXBlockquote, type MDXBlockquoteProps } from './MDXBlockquote';
export { MDXTable } from './MDXTable';
export { MDXHeading } from './MDXHeading';
export { MDXFigure } from './MDXFigure';

// ==================== 代码组件导出 ====================
export {
  MDXCodeInline,
  PrimaryCode,
  SecondaryCode,
  SuccessCode,
  WarningCode,
  ErrorCode,
  type MDXCodeInlineProps,
} from './mdx-code-inline';
export { MDXCodeGroup } from './MDXCodeGroup';
export { MDXCodeDemo, type MDXCodeDemoProps } from './MDXCodeDemo';

// ==================== 扩展组件导出 ====================
export { MDXTabs, type MDXTabsProps } from './MDXTabs';
export { MDXAccordion, type MDXAccordionProps } from './MDXAccordion';
export { MDXVideo, type MDXVideoProps } from './MDXVideo';
export { MDXImageZoom, type MDXImageZoomProps } from './MDXImageZoom';
export { MDXCallout } from './MDXCallout';
export { MDXCard } from './MDXCard';
