/**
 * 统一工具函数导出
 * 提供项目中所有工具函数的统一入口
 */

// ==================== 样式工具 ====================
export { cn } from './styles';

// ==================== 文本处理工具 ====================
export { countWords, formatReadingTime, slugify } from './text';

// ==================== DOM 操作工具 ====================
export { scrollToElement, isElementInViewport, getElementPosition } from './dom';

// ==================== 路由工具 ====================
export { buildTagLink, buildCategoryLink } from './route';

// ==================== 配置工具 ====================
export { MDX_CONFIG } from './config';

// ==================== 通用辅助函数 ====================
export * from './helpers';

// ==================== 导入顺序规范 ====================
export * from './import-order';

// ==================== 工具函数重新导出 ====================
export * from './tools';
