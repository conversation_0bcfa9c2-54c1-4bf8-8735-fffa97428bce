---
title: React 核心库详解
description: 深入解析 React 核心库的设计理念、基础特性和最佳实践，包括组件开发、状态管理、生命周期以及开发模式与生产模式的区别
date: "2025-04-07"
category: "前端开发"
tags:
- React
- JavaScript
- 组件开发
- 状态管理
- 虚拟DOM
- Hooks
---

React 是一个用于创建用户界面的 JavaScript 库。

`react` 包仅包含定义 React 组件所需的功能。它通常与 React 渲染器一起使用，例如用于网页的 `react-dom` 或用于原生环境的 `react-native`。

**注意：** 默认情况下，React 将处于开发模式。开发版本包含关于常见错误的额外警告，而生产版本则包含额外的性能优化并删除所有错误消息。部署应用程序时，请务必使用 [生产构建](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)。

## 用法

```js
import { useState } from 'react';
import { createRoot } from 'react-dom/client';

function Counter() {
  const [count, setCount] = useState(0);
  return (
    <>
      <h1>{count}</h1>
      <button onClick={() => setCount(count + 1)}>
        Increment
      </button>
    </>
  );
}

const root = createRoot(document.getElementById('root'));
root.render(<Counter />);
```

## 文档

参考 https://react.dev/

## API

参考 https://react.dev/reference/react


## 源码

### `index.js`

`index.js` 是 React 的入口文件。它根据环境变量 `NODE_ENV` 来决定导入哪个版本的 React。

- 如果 `NODE_ENV` 是 `production`，则导入 `react.production.js`。
- 如果 `NODE_ENV` 不是 `production`，则导入 `react.development.js`。

```js
'use strict';

if (process.env.NODE_ENV === 'production') {
  module.exports = require('./cjs/react.production.js');
} else {
  module.exports = require('./cjs/react.development.js');
}
```

`react.production.js` 是 React 的生产版本，它包含额外的性能优化并删除所有错误消息。

`react.development.js` 是 React 的开发版本，它包含关于常见错误的额外警告。

### `compiler-runtime.js`

`compiler-runtime.js` 是 React 的编译器运行时。它根据环境变量 `NODE_ENV` 来决定导入哪个版本的 React 编译器运行时。

```js
'use strict';

if (process.env.NODE_ENV === 'production') {
  module.exports = require('./cjs/react-compiler-runtime.production.js');
} else {
  module.exports = require('./cjs/react-compiler-runtime.development.js');
}
```

### `jsx-runtime.js`

`jsx-runtime.js` 是 React 的 JSX 运行时。它根据环境变量 `NODE_ENV` 来决定导入哪个版本的 React JSX 运行时。

```js
'use strict';

if (process.env.NODE_ENV === 'production') {
  module.exports = require('./cjs/react-jsx-runtime.production.js');
} else {
  module.exports = require('./cjs/react-jsx-runtime.development.js');
}
```

### `jsx-runtime.react-server.js`

`jsx-runtime.react-server.js` 是 React 的 JSX 运行时。它根据环境变量 `NODE_ENV` 来决定导入哪个版本的 React JSX 运行时。

```js
'use strict';

if (process.env.NODE_ENV === 'production') {
  module.exports = require('./cjs/react-jsx-dev-runtime.react-server.production.js');
} else {
  module.exports = require('./cjs/react-jsx-dev-runtime.react-server.development.js');
}
```

### `jsx-dev-runtime.js`

`jsx-dev-runtime.js` 是 React 的 JSX 运行时。它根据环境变量 `NODE_ENV` 来决定导入哪个版本的 React JSX 运行时。

```js
'use strict';

if (process.env.NODE_ENV === 'production') {
  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');
} else {
  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');
}
```

### `jsx-dev-runtime.react-server.js`

`jsx-dev-runtime.react-server.js` 是 React 的 JSX 运行时。它根据环境变量 `NODE_ENV` 来决定导入哪个版本的 React JSX 运行时。

```js
'use strict';

if (process.env.NODE_ENV === 'production') {
  module.exports = require('./cjs/react-jsx-dev-runtime.react-server.production.js');
} else {
  module.exports = require('./cjs/react-jsx-dev-runtime.react-server.development.js');
}
```

### `react.react-server.js`

`react.react-server.js` 是 React 的 JSX 运行时。它根据环境变量 `NODE_ENV` 来决定导入哪个版本的 React JSX 运行时。

```js
'use strict';

if (process.env.NODE_ENV === 'production') {
  module.exports = require('./cjs/react.react-server.production.js');
} else {
  module.exports = require('./cjs/react.react-server.development.js');
}
```