'use client';

import React from 'react';
import { cn } from '@/utils';

export interface MDXBlockquoteProps extends React.BlockquoteHTMLAttributes<HTMLQuoteElement> {
  /** 引用内容 */
  children: React.ReactNode;
  /** 引用来源 */
  cite?: string;
  /** 作者 */
  author?: string;
  /** 样式变体 */
  variant?: 'default' | 'info' | 'warning' | 'success' | 'error';
  /** 自定义类名 */
  className?: string;
}

/**
 * 样式变体映射
 */
const variantStyles = {
  default: {
    border: 'border-l-primary',
    bg: 'bg-muted/30',
    text: 'text-muted-foreground',
  },
  info: {
    border: 'border-l-blue-500',
    bg: 'bg-blue-50 dark:bg-blue-950/30',
    text: 'text-blue-700 dark:text-blue-300',
  },
  warning: {
    border: 'border-l-amber-500',
    bg: 'bg-amber-50 dark:bg-amber-950/30',
    text: 'text-amber-700 dark:text-amber-300',
  },
  success: {
    border: 'border-l-green-500',
    bg: 'bg-green-50 dark:bg-green-950/30',
    text: 'text-green-700 dark:text-green-300',
  },
  error: {
    border: 'border-l-red-500',
    bg: 'bg-red-50 dark:bg-red-950/30',
    text: 'text-red-700 dark:text-red-300',
  },
} as const;

/**
 * MDX 引用块组件
 *
 * 功能：
 * 1. 支持多种样式变体
 * 2. 可选的作者和来源信息
 * 3. 响应式设计
 * 4. 主题适配
 * 5. 优雅的视觉效果
 */
export const MDXBlockquote = React.forwardRef<HTMLQuoteElement, MDXBlockquoteProps>(
  ({ children, cite, author, variant = 'default', className, ...props }, ref) => {
    const styles = variantStyles[variant];

    return (
      <blockquote
        ref={ref}
        cite={cite}
        className={cn(
          // 基础样式
          'my-6 pl-6 pr-4 py-4',
          'border-l-4 rounded-r-lg',
          'italic font-medium',
          'relative',

          // 变体样式
          styles.border,
          styles.bg,
          styles.text,

          // 自定义样式
          className
        )}
        {...props}
      >
        {/* 引用内容 */}
        <div className="relative">
          {/* 引号装饰 */}
          <span
            className={cn(
              'absolute -left-2 -top-2',
              'text-4xl font-bold opacity-20',
              'select-none pointer-events-none'
            )}
            aria-hidden="true"
          >
            "
          </span>

          {/* 引用文本 */}
          <div className="relative z-10">{children}</div>
        </div>

        {/* 作者或来源信息 */}
        {(author || cite) && (
          <footer className={cn('mt-4 text-sm not-italic', 'opacity-80')}>
            {author && <cite className="font-medium">— {author}</cite>}
            {cite && !author && <cite className="font-medium">— {cite}</cite>}
          </footer>
        )}
      </blockquote>
    );
  }
);

MDXBlockquote.displayName = 'MDXBlockquote';

// 样式变体组件
export const InfoBlockquote = (props: Omit<MDXBlockquoteProps, 'variant'>) => (
  <MDXBlockquote variant="info" {...props} />
);

export const WarningBlockquote = (props: Omit<MDXBlockquoteProps, 'variant'>) => (
  <MDXBlockquote variant="warning" {...props} />
);

export const SuccessBlockquote = (props: Omit<MDXBlockquoteProps, 'variant'>) => (
  <MDXBlockquote variant="success" {...props} />
);

export const ErrorBlockquote = (props: Omit<MDXBlockquoteProps, 'variant'>) => (
  <MDXBlockquote variant="error" {...props} />
);

export default MDXBlockquote;
