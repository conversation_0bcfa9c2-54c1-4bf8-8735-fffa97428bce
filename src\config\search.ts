/**
 * 搜索相关配置
 * 统一管理搜索功能的配置、命令和常量
 */

import { Command } from '@/types';

// ==================== 搜索命令配置 ====================

/**
 * 快捷命令列表
 */
export const SEARCH_COMMANDS: Command[] = [
  {
    id: 'theme',
    title: '切换主题',
    description: '在亮色和暗色主题之间切换',
    action: () => document.documentElement.classList.toggle('dark'),
    shortcut: 'Ctrl+Shift+T',
    group: 'system',
  },
  {
    id: 'docs',
    title: '查看文档',
    description: '跳转到文档首页',
    action: () => (window.location.href = '/docs'),
    shortcut: 'Ctrl+D',
    group: 'navigation',
  },
  {
    id: 'blog',
    title: '查看博客',
    description: '跳转到博客首页',
    action: () => (window.location.href = '/blog'),
    shortcut: 'Ctrl+B',
    group: 'navigation',
  },
  {
    id: 'tools',
    title: '查看工具',
    description: '跳转到工具页面',
    action: () => (window.location.href = '/tools'),
    shortcut: 'Ctrl+T',
    group: 'navigation',
  },
  {
    id: 'links',
    title: '网址导航',
    description: '跳转到网址导航页面',
    action: () => (window.location.href = '/links'),
    shortcut: 'Ctrl+L',
    group: 'navigation',
  },
];

// ==================== 搜索常量配置 ====================

/**
 * 本地存储键名
 */
export const SEARCH_STORAGE_KEYS = {
  /** 搜索历史记录 */
  HISTORY: 'search-history',
  /** 搜索偏好设置 */
  PREFERENCES: 'search-preferences',
  /** 最近搜索 */
  RECENT: 'search-recent',
} as const;

/**
 * 搜索结果分类显示文本
 */
export const SEARCH_TYPE_LABELS = {
  tool: '工具',
  navigation: '网址导航',
  doc: '文档',
  blog: '博客',
  command: '命令',
  history: '历史记录',
  link: '链接',
  web: '网页',
} as const;

/**
 * 搜索配置参数
 */
export const SEARCH_CONFIG = {
  /** 最大搜索结果数量 */
  MAX_RESULTS: 50,
  /** 搜索防抖延迟（毫秒） */
  DEBOUNCE_DELAY: 300,
  /** 最小查询长度 */
  MIN_QUERY_LENGTH: 1,
  /** 最大历史记录数量 */
  MAX_HISTORY_ITEMS: 20,
  /** 搜索结果高亮类名 */
  HIGHLIGHT_CLASS: 'search-highlight',
  /** 搜索超时时间（毫秒） */
  SEARCH_TIMEOUT: 5000,
} as const;

/**
 * 搜索优先级配置
 */
export const SEARCH_PRIORITY = {
  command: 100,
  tool: 90,
  doc: 80,
  blog: 70,
  navigation: 60,
  link: 50,
  history: 40,
  web: 30,
} as const;

/**
 * 搜索过滤器配置
 */
export const SEARCH_FILTERS = {
  /** 内容类型过滤器 */
  CONTENT_TYPES: ['all', 'doc', 'blog', 'tool', 'link'] as const,
  /** 时间范围过滤器 */
  TIME_RANGES: ['all', 'today', 'week', 'month', 'year'] as const,
  /** 排序方式 */
  SORT_OPTIONS: ['relevance', 'date', 'title', 'type'] as const,
} as const;

// ==================== 搜索 API 配置 ====================

/**
 * 搜索 API 端点
 */
export const SEARCH_API_ENDPOINTS = {
  /** 全局搜索 */
  GLOBAL: '/api/search',
  /** 博客搜索 */
  BLOG: '/api/search/blog',
  /** 文档搜索 */
  DOCS: '/api/search/docs',
  /** 工具搜索 */
  TOOLS: '/api/search/tools',
  /** 链接搜索 */
  LINKS: '/api/search/links',
} as const;

/**
 * 搜索请求配置
 */
export const SEARCH_REQUEST_CONFIG = {
  /** 默认请求头 */
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json',
  },
  /** 请求超时时间 */
  TIMEOUT: 10000,
  /** 重试次数 */
  RETRY_COUNT: 3,
  /** 重试延迟 */
  RETRY_DELAY: 1000,
} as const;

// ==================== 类型定义 ====================

export type SearchType = keyof typeof SEARCH_TYPE_LABELS;
export type SearchFilter = typeof SEARCH_FILTERS.CONTENT_TYPES[number];
export type SearchTimeRange = typeof SEARCH_FILTERS.TIME_RANGES[number];
export type SearchSortOption = typeof SEARCH_FILTERS.SORT_OPTIONS[number];

/**
 * 搜索选项接口
 */
export interface SearchOptions {
  /** 查询字符串 */
  query: string;
  /** 内容类型过滤 */
  type?: SearchFilter;
  /** 时间范围过滤 */
  timeRange?: SearchTimeRange;
  /** 排序方式 */
  sort?: SearchSortOption;
  /** 最大结果数量 */
  limit?: number;
  /** 偏移量 */
  offset?: number;
  /** 是否包含高亮 */
  highlight?: boolean;
}

/**
 * 搜索偏好设置接口
 */
export interface SearchPreferences {
  /** 默认搜索类型 */
  defaultType: SearchFilter;
  /** 默认排序方式 */
  defaultSort: SearchSortOption;
  /** 是否启用搜索历史 */
  enableHistory: boolean;
  /** 是否启用搜索建议 */
  enableSuggestions: boolean;
  /** 是否启用实时搜索 */
  enableRealtime: boolean;
}

// ==================== 默认配置 ====================

/**
 * 默认搜索偏好设置
 */
export const DEFAULT_SEARCH_PREFERENCES: SearchPreferences = {
  defaultType: 'all',
  defaultSort: 'relevance',
  enableHistory: true,
  enableSuggestions: true,
  enableRealtime: true,
};

/**
 * 默认搜索选项
 */
export const DEFAULT_SEARCH_OPTIONS: Partial<SearchOptions> = {
  type: 'all',
  sort: 'relevance',
  limit: SEARCH_CONFIG.MAX_RESULTS,
  offset: 0,
  highlight: true,
};
