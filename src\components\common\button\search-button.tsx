'use client';

import React, { useState, useEffect } from 'react';
import { Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { SearchDialog } from '@/components/features/search/search-dialog';

/**
 * 搜索图标组件
 * 点击后打开搜索对话框
 */
export function SearchIcon() {
  const [open, setOpen] = useState(false);

  // 监听键盘快捷键 (Ctrl+K 或 Command+K)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        setOpen((prev) => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  return (
    <>
      <Button
        variant="ghost"
        size="icon"
        className="h-9 w-9 text-muted-foreground hover:text-foreground"
        onClick={() => setOpen(true)}
        aria-label="搜索"
        title="搜索（Ctrl + K）"
      >
        <Search className="h-5 w-5" />
      </Button>
      <SearchDialog open={open} onOpenChangeAction={setOpen} />
    </>
  );
}
