/**
 * 工具状态存储
 * 管理所有工具页面的状态
 */

import { create } from 'zustand';
import { persist, subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import type { ToolPageState, ToolHistoryItem } from './types';
import { STATE_CONFIG } from './config';

/**
 * 工具状态存储
 */
export const useToolStore = create<ToolPageState>()(
  subscribeWithSelector(
    persist(
      immer((set, _get) => ({
        // ==================== 基础工具状态 ====================
        input: '',
        output: '',
        loading: false,
        error: null,
        history: [],

        // ==================== 工具页面状态 ====================
        activeTab: 'input',

        // ==================== 状态操作方法 ====================
        setInput: (input: string) => {
          set(state => {
            state.input = input;
            state.error = null; // 清除错误状态
          });
        },

        setOutput: (output: string) => {
          set(state => {
            state.output = output;
          });
        },

        setLoading: (loading: boolean) => {
          set(state => {
            state.loading = loading;
          });
        },

        setError: (error: string | null) => {
          set(state => {
            state.error = error;
            if (error) {
              state.output = ''; // 有错误时清空输出
            }
          });
        },

        addToHistory: (item: ToolHistoryItem) => {
          set(state => {
            // 避免重复添加相同的历史记录
            const exists = state.history.some(
              h => h.input === item.input && h.output === item.output && h.tool === item.tool
            );

            if (!exists) {
              state.history = [item, ...state.history].slice(0, STATE_CONFIG.MAX_TOOL_HISTORY);
            }
          });
        },

        clearHistory: () => {
          set(state => {
            state.history = [];
          });
        },

        reset: () => {
          set(state => {
            state.input = '';
            state.output = '';
            state.loading = false;
            state.error = null;
          });
        },

        setActiveTab: (tab: string) => {
          set(state => {
            state.activeTab = tab;
          });
        },
      })),
      {
        name: 'iflux-tool-storage',
        partialize: state => ({
          history: state.history,
          activeTab: state.activeTab,
        }),
        skipHydration: true, // 跳过水合，避免 SSR 问题
      }
    )
  )
);

// ==================== 工具状态选择器 ====================

/**
 * 基础工具状态选择器
 */
export const useToolState = () =>
  useToolStore(state => ({
    input: state.input,
    output: state.output,
    loading: state.loading,
    error: state.error,
    setInput: state.setInput,
    setOutput: state.setOutput,
    setLoading: state.setLoading,
    setError: state.setError,
    reset: state.reset,
  }));

/**
 * 工具历史记录选择器
 */
export const useToolHistory = () =>
  useToolStore(state => ({
    history: state.history,
    addToHistory: state.addToHistory,
    clearHistory: state.clearHistory,
  }));

/**
 * 工具页面选择器
 */
export const useToolPage = () =>
  useToolStore(state => ({
    activeTab: state.activeTab,
    setActiveTab: state.setActiveTab,
  }));

// ==================== 工具特定的 Hooks ====================

/**
 * 计算器状态 Hook
 */
export const useCalculatorState = () => {
  const toolState = useToolState();
  const toolHistory = useToolHistory();

  return {
    ...toolState,
    ...toolHistory,
    // 计算器特定的方法
    calculate: (expression: string) => {
      toolState.setLoading(true);
      try {
        // 这里可以添加计算逻辑
        const result = eval(expression); // 注意：实际使用中应该用更安全的计算方法
        toolState.setOutput(result.toString());
        toolHistory.addToHistory({
          id: Date.now().toString(),
          timestamp: Date.now(),
          input: expression,
          output: result.toString(),
          tool: 'calculator',
        });
      } catch (_error) {
        toolState.setError('计算错误');
      } finally {
        toolState.setLoading(false);
      }
    },
  };
};

/**
 * Base64 编码器状态 Hook
 */
export const useBase64State = () => {
  const toolState = useToolState();
  const toolHistory = useToolHistory();

  return {
    ...toolState,
    ...toolHistory,
    // Base64 特定的方法
    encode: (text: string) => {
      try {
        // 使用现代的 TextEncoder 替代弃用的 unescape
        const encoder = new TextEncoder();
        const data = encoder.encode(text);
        const encoded = btoa(String.fromCharCode(...data));
        toolState.setOutput(encoded);
        toolHistory.addToHistory({
          id: Date.now().toString(),
          timestamp: Date.now(),
          input: text,
          output: encoded,
          tool: 'base64-encoder',
        });
      } catch (_error) {
        toolState.setError('编码失败');
      }
    },
    decode: (encoded: string) => {
      try {
        // 使用现代的 TextDecoder 替代弃用的 escape
        const binaryString = atob(encoded);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }
        const decoder = new TextDecoder();
        const decoded = decoder.decode(bytes);
        toolState.setOutput(decoded);
        toolHistory.addToHistory({
          id: Date.now().toString(),
          timestamp: Date.now(),
          input: encoded,
          output: decoded,
          tool: 'base64-decoder',
        });
      } catch (_error) {
        toolState.setError('解码失败');
      }
    },
  };
};

/**
 * 文本计数器状态 Hook
 */
export const useTextCounterState = () => {
  const toolState = useToolState();

  return {
    ...toolState,
    // 文本计数器特定的方法
    count: (text: string) => {
      const stats = {
        characters: text.length,
        charactersNoSpaces: text.replace(/\s/g, '').length,
        words: text.trim() ? text.trim().split(/\s+/).length : 0,
        lines: text.split('\n').length,
        paragraphs: text.split(/\n\s*\n/).filter(p => p.trim()).length,
      };

      const output = `字符数: ${stats.characters}
字符数(不含空格): ${stats.charactersNoSpaces}
单词数: ${stats.words}
行数: ${stats.lines}
段落数: ${stats.paragraphs}`;

      toolState.setOutput(output);
    },
  };
};

/**
 * 正则表达式测试器状态 Hook
 */
export const useRegexTesterState = () => {
  const toolState = useToolState();
  const toolHistory = useToolHistory();

  return {
    ...toolState,
    ...toolHistory,
    // 正则表达式测试器特定的方法
    test: (pattern: string, text: string, flags: string = 'g') => {
      try {
        const regex = new RegExp(pattern, flags);
        const matches = Array.from(text.matchAll(regex));

        const output =
          matches.length > 0
            ? `找到 ${matches.length} 个匹配项:\n${matches
                .map((match, index) => `${index + 1}. ${match[0]} (位置: ${match.index})`)
                .join('\n')}`
            : '没有找到匹配项';

        toolState.setOutput(output);
        toolHistory.addToHistory({
          id: Date.now().toString(),
          timestamp: Date.now(),
          input: `模式: ${pattern}\n文本: ${text}`,
          output,
          tool: 'regex-tester',
        });
      } catch (_error) {
        toolState.setError('正则表达式语法错误');
      }
    },
  };
};

/**
 * 时间戳转换器状态 Hook
 */
export const useTimestampConverterState = () => {
  const toolState = useToolState();
  const toolHistory = useToolHistory();

  return {
    ...toolState,
    ...toolHistory,
    // 时间戳转换器特定的方法
    convertToTimestamp: (dateString: string) => {
      try {
        const timestamp = new Date(dateString).getTime();
        if (isNaN(timestamp)) {
          throw new Error('Invalid date');
        }
        toolState.setOutput(timestamp.toString());
        toolHistory.addToHistory({
          id: Date.now().toString(),
          timestamp: Date.now(),
          input: dateString,
          output: timestamp.toString(),
          tool: 'timestamp-converter',
        });
      } catch (_error) {
        toolState.setError('日期格式错误');
      }
    },
    convertToDate: (timestamp: string) => {
      try {
        const date = new Date(parseInt(timestamp));
        if (isNaN(date.getTime())) {
          throw new Error('Invalid timestamp');
        }
        const output = date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        });
        toolState.setOutput(output);
        toolHistory.addToHistory({
          id: Date.now().toString(),
          timestamp: Date.now(),
          input: timestamp,
          output,
          tool: 'timestamp-converter',
        });
      } catch (_error) {
        toolState.setError('时间戳格式错误');
      }
    },
  };
};
