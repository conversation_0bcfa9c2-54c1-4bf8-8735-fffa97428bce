/**
 * 客户端专用状态存储 Hook
 * 解决 Zustand 在 SSR 环境下的水合问题
 */

'use client';

import { useEffect, useState } from 'react';
import { useAppStore } from '@/stores/app-store';
import { useToolStore } from '@/stores/tool-store';

/**
 * 安全的客户端状态 Hook
 * 在服务器端返回默认值，在客户端返回实际状态
 */
export function useClientStore<T>(selector: (state: any) => T, defaultValue: T): T {
  const [isClient, setIsClient] = useState(false);
  const storeValue = useAppStore(selector);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient ? storeValue : defaultValue;
}

/**
 * 安全的导航栏状态 Hook
 */
export function useClientNavbar() {
  const defaultNavbarState = {
    direction: 'up' as const,
    position: 0,
    showTitle: false,
    pageTitle: '',
    lastDirectionChange: 0,
    setScrollPosition: () => {},
    setPageTitle: () => {},
    scrollToTop: () => {},
  };

  return useClientStore(
    state => ({
      direction: state.direction,
      position: state.position,
      showTitle: state.showTitle,
      pageTitle: state.pageTitle,
      lastDirectionChange: state.lastDirectionChange,
      setScrollPosition: state.setScrollPosition,
      setPageTitle: state.setPageTitle,
      scrollToTop: state.scrollToTop,
    }),
    defaultNavbarState
  );
}

/**
 * 安全的主题状态 Hook
 */
export function useClientTheme() {
  const defaultThemeState = {
    theme: 'system' as const,
    setTheme: () => {},
  };

  return useClientStore(
    state => ({
      theme: state.theme,
      setTheme: state.setTheme,
    }),
    defaultThemeState
  );
}

/**
 * 安全的认证状态 Hook
 */
export function useClientAuth() {
  const defaultAuthState = {
    isLoggedIn: false,
    loginTime: null,
    setLoginState: () => {},
    logout: () => {},
    checkLoginExpiry: () => false,
  };

  return useClientStore(
    state => ({
      isLoggedIn: state.isLoggedIn,
      loginTime: state.loginTime,
      setLoginState: state.setLoginState,
      logout: state.logout,
      checkLoginExpiry: state.checkLoginExpiry,
    }),
    defaultAuthState
  );
}

/**
 * 安全的搜索状态 Hook
 */
export function useClientSearch() {
  const defaultSearchState = {
    isOpen: false,
    query: '',
    results: [],
    isLoading: false,
    history: [],
    selectedIndex: 0,
    setOpen: () => {},
    setQuery: () => {},
    setResults: () => {},
    setLoading: () => {},
    addToHistory: () => {},
    clearHistory: () => {},
    setSelectedIndex: () => {},
    resetSearch: () => {},
  };

  return useClientStore(
    state => ({
      isOpen: state.isOpen,
      query: state.query,
      results: state.results,
      isLoading: state.isLoading,
      history: state.history,
      selectedIndex: state.selectedIndex,
      setOpen: state.setOpen,
      setQuery: state.setQuery,
      setResults: state.setResults,
      setLoading: state.setLoading,
      addToHistory: state.addToHistory,
      clearHistory: state.clearHistory,
      setSelectedIndex: state.setSelectedIndex,
      resetSearch: state.resetSearch,
    }),
    defaultSearchState
  );
}

/**
 * 安全的过滤状态 Hook
 */
export function useClientFilter() {
  const defaultFilterState = {
    selectedCategory: '',
    selectedTag: null,
    searchTerm: '',
    setCategory: () => {},
    setTag: () => {},
    setSearchTerm: () => {},
    reset: () => {},
  };

  return useClientStore(
    state => ({
      selectedCategory: state.selectedCategory,
      selectedTag: state.selectedTag,
      searchTerm: state.searchTerm,
      setCategory: state.setCategory,
      setTag: state.setTag,
      setSearchTerm: state.setSearchTerm,
      reset: state.reset,
    }),
    defaultFilterState
  );
}

/**
 * 安全的缓存状态 Hook
 */
export function useClientCache() {
  const defaultCacheState = {
    set: () => {},
    get: () => null,
    remove: () => {},
    clear: () => {},
    cleanup: () => {},
  };

  return useClientStore(
    state => ({
      set: state.set,
      get: state.get,
      remove: state.remove,
      clear: state.clear,
      cleanup: state.cleanup,
    }),
    defaultCacheState
  );
}

/**
 * 安全的工具状态 Hook
 */
export function useClientToolState() {
  const [isClient, setIsClient] = useState(false);
  const storeValue = useToolStore(state => ({
    input: state.input,
    output: state.output,
    loading: state.loading,
    error: state.error,
    setInput: state.setInput,
    setOutput: state.setOutput,
    setLoading: state.setLoading,
    setError: state.setError,
    reset: state.reset,
  }));

  const defaultToolState = {
    input: '',
    output: '',
    loading: false,
    error: null,
    setInput: () => {},
    setOutput: () => {},
    setLoading: () => {},
    setError: () => {},
    reset: () => {},
  };

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient ? storeValue : defaultToolState;
}
