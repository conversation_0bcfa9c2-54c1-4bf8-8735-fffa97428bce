{"name": "web", "version": "0.1.0", "private": true, "packageManager": "pnpm@9.11.0", "engines": {"node": ">=20.11.1", "pnpm": ">=9.11.0"}, "scripts": {"dev": "next dev", "clean": "rimraf .next", "build": "pnpm run clean && next build", "start": "next start", "lint": "next lint", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,scss,md}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,css,scss,md}\"", "preview": "next build && next start", "update-deps": "pnpm update", "prepare": "husky"}, "repository": {"type": "git", "url": ""}, "dependencies": {"@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@next/mdx": "^15.3.2", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "cheerio": "^1.0.0", "chokidar": "^4.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.18.1", "gray-matter": "^4.0.3", "lodash": "^4.17.21", "lucide-react": "^0.510.0", "next": "^15.3.2", "next-mdx-remote": "^5.0.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-intersection-observer": "^9.16.0", "rehype-autolink-headings": "^7.1.0", "rehype-slug": "^6.0.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.0", "zod": "^3.25.67"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.8", "@tailwindcss/typography": "^0.5.16", "@types/chokidar": "^2.1.7", "@types/lodash": "^4.17.18", "@types/node": "^20.17.51", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "eslint": "^9.27.0", "eslint-config-next": "15.3.2", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.2.5", "rehype-pretty-code": "^0.14.1", "rimraf": "^6.0.1", "shiki": "^3.7.0", "tailwindcss": "^4.1.7", "typescript": "^5.8.3"}, "pnpm": {"overrides": {}}, "lint-staged": {"**/*.{js,jsx,ts,tsx,json,css,scss,md}": ["prettier --write"]}}