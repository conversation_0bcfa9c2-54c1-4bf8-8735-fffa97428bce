'use client';

import React, { useState, useEffect, useCallback, forwardRef } from 'react';
import { Maximize2, Minimize2, Code, Eye, Copy, Check, Columns2 } from 'lucide-react';
import { cn } from '@/utils';

export interface MDXCodeDemoProps {
  code: string;
  preview: React.ReactNode;
  title?: string;
  description?: string;
  language?: string;
  defaultView?: 'code' | 'preview' | 'split';
  className?: string;
}

/**
 * MDX 代码示例组件
 * - 支持代码和预览切换
 * - 支持分屏模式
 * - 支持全屏预览
 * - 代码高亮
 * - 支持复制代码
 * - 响应式设计
 */
export const MDXCodeDemo = forwardRef<HTMLDivElement, MDXCodeDemoProps>(
  (
    {
      code,
      preview,
      title,
      description,
      language = 'jsx',
      defaultView = 'split',
      className,
      ...props
    },
    ref
  ) => {
    const [view, setView] = useState<'code' | 'preview' | 'split'>(defaultView);
    const [isFullscreen, setIsFullscreen] = useState(false);
    const [copied, setCopied] = useState(false);

    // 合并外部 ref 和内部 ref
    const containerRef = React.useRef<HTMLDivElement>(null);
    React.useImperativeHandle(ref, () => containerRef.current!, []);

    // 复制代码
    const handleCopy = useCallback(async () => {
      try {
        await navigator.clipboard.writeText(code);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (err) {
        console.error('Failed to copy code:', err);
      }
    }, [code]);

    // 切换全屏
    const toggleFullscreen = useCallback(() => {
      if (!containerRef.current) return;

      if (!isFullscreen) {
        if (containerRef.current.requestFullscreen) {
          containerRef.current.requestFullscreen();
        }
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        }
      }
    }, [isFullscreen]);

    // 监听全屏变化
    useEffect(() => {
      const handleFullscreenChange = () => {
        setIsFullscreen(!!document.fullscreenElement);
      };

      document.addEventListener('fullscreenchange', handleFullscreenChange);
      return () => {
        document.removeEventListener('fullscreenchange', handleFullscreenChange);
      };
    }, []);

    return (
      <div
        ref={containerRef}
        className={cn(
          'my-6 overflow-hidden rounded-lg',
          'border border-border bg-card shadow-sm',
          isFullscreen && 'fixed inset-0 z-50 bg-background',
          className
        )}
        {...props}
      >
        {/* 头部工具栏 */}
        <div
          className={cn(
            'flex items-center justify-between',
            'px-4 py-3 border-b border-border',
            'bg-muted/30'
          )}
        >
          <div className="flex items-center gap-3">
            {title && <h3 className="text-sm font-semibold text-foreground">{title}</h3>}
          </div>

          <div className="flex items-center gap-2">
            {/* 视图切换 */}
            <div className="flex rounded-md overflow-hidden border border-border bg-muted/50">
              <button
                type="button"
                onClick={() => setView('code')}
                className={cn(
                  'px-3 py-1.5 flex items-center gap-1.5',
                  'text-xs font-medium transition-colors',
                  'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
                  view === 'code'
                    ? 'bg-background text-foreground shadow-sm'
                    : 'text-muted-foreground hover:text-foreground hover:bg-muted'
                )}
                aria-pressed={view === 'code'}
              >
                <Code className="h-3.5 w-3.5" />
                <span className="hidden sm:inline">代码</span>
              </button>
              <button
                type="button"
                onClick={() => setView('preview')}
                className={cn(
                  'px-3 py-1.5 flex items-center gap-1.5',
                  'text-xs font-medium transition-colors',
                  'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
                  view === 'preview'
                    ? 'bg-background text-foreground shadow-sm'
                    : 'text-muted-foreground hover:text-foreground hover:bg-muted'
                )}
                aria-pressed={view === 'preview'}
              >
                <Eye className="h-3.5 w-3.5" />
                <span className="hidden sm:inline">预览</span>
              </button>
              <button
                type="button"
                onClick={() => setView('split')}
                className={cn(
                  'px-3 py-1.5 flex items-center gap-1.5',
                  'text-xs font-medium transition-colors',
                  'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
                  view === 'split'
                    ? 'bg-background text-foreground shadow-sm'
                    : 'text-muted-foreground hover:text-foreground hover:bg-muted'
                )}
                aria-pressed={view === 'split'}
              >
                <Columns2 className="h-3.5 w-3.5" />
                <span className="hidden sm:inline">分屏</span>
              </button>
            </div>

            {/* 操作按钮 */}
            <div className="flex items-center gap-1">
              <button
                type="button"
                onClick={handleCopy}
                className={cn(
                  'p-2 rounded-md transition-colors',
                  'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
                  copied
                    ? 'text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-950'
                    : 'text-muted-foreground hover:text-foreground hover:bg-muted'
                )}
                aria-label={copied ? '已复制' : '复制代码'}
              >
                {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
              </button>

              <button
                type="button"
                onClick={toggleFullscreen}
                className={cn(
                  'p-2 rounded-md transition-colors',
                  'text-muted-foreground hover:text-foreground hover:bg-muted',
                  'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2'
                )}
                aria-label={isFullscreen ? '退出全屏' : '全屏'}
              >
                {isFullscreen ? (
                  <Minimize2 className="h-4 w-4" />
                ) : (
                  <Maximize2 className="h-4 w-4" />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* 描述信息 */}
        {description && (
          <div className="px-4 py-2 text-sm text-muted-foreground border-b border-border bg-muted/20">
            {description}
          </div>
        )}

        {/* 内容区域 */}
        <div
          className={cn(
            'min-h-0', // 防止 flex 子元素溢出
            view === 'split' && [
              'grid grid-cols-1 lg:grid-cols-2',
              'divide-y lg:divide-y-0 lg:divide-x divide-border',
            ]
          )}
        >
          {/* 代码区域 */}
          {(view === 'code' || view === 'split') && (
            <div className="overflow-auto bg-muted/10">
              <pre
                className={cn(
                  `language-${language}`,
                  'p-4 m-0 text-sm font-mono',
                  'bg-transparent border-0',
                  'overflow-x-auto'
                )}
              >
                <code className="text-inherit bg-transparent">{code}</code>
              </pre>
            </div>
          )}

          {/* 预览区域 */}
          {(view === 'preview' || view === 'split') && (
            <div className="p-4 overflow-auto bg-background min-h-[200px] flex items-center justify-center">
              <div className="w-full">{preview}</div>
            </div>
          )}
        </div>
      </div>
    );
  }
);

MDXCodeDemo.displayName = 'MDXCodeDemo';
