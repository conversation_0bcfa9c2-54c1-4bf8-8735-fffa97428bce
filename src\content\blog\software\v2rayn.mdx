---
title: V2rayN 客户端配置指南
description: V2rayN Windows 客户端的下载安装、节点导入和代理设置的完整教程
date: 2025-01-14
category: 软件
tags:
- V2rayN
- 代理工具
- 网络配置
- Windows
---

## 使用教程

### 软件下载

> 下载地址：[https://github.com/2dust/v2rayN/releases/download/3.23/v2rayN-Core.zip](https://github.com/2dust/v2rayN/releases/download/3.23/v2rayN-Core.zip)

下载后解压到自己喜欢的文件夹。

![下载v2rayN](https://img.dava.cc/img/下载v2rayN.png)

双击打开 `v2rayN.exe`，可以看到如下程序主界面。

> 如果 Windows 出现安全提示，点击「更多信息」然后再选择「仍要运行」即可。
>
> 双击后如果没反应，可以在右下角状态栏找到 v 2 ray 图标（蓝底 V 字），左键单击图标，软件界面就出来了。

![v2rayN主界面](https://img.dava.cc/img/v2rayN主界面.png)

### 导入节点

复制 `vmess` 节点（[获取地址](https://github.com/freefq/free)），在 v2RayN 主界面点击服务器，在下拉菜单中选择从剪贴板导入批量 URL。

![导入v2rayN节点](https://img.dava.cc/img/导入v2rayN节点.png)

### 启用节点

选择任一节点，单击右键->设为活动服务器，即启用该节点作为 HTTP 代理服务器。

> 也可以左键点击节点名称后，按一下回车（Enter）键。

![启用v2rayN节点](https://img.dava.cc/img/启用v2rayN节点.png)

## 设置代理

最新版 V2RayN 的 `http` 代理默认是关闭关闭，可以在任务栏找到蓝底 V 字图标，右键点击，悬停在 http代理选项卡上，在弹出选项中选择开启 PAC 或全局模式（推荐 PAC 模式）。

![设置v2rayN代理](https://img.dava.cc/img/设置v2rayN代理.png)

打开浏览器，尝试访问外网链接，看是否可用。

## 参考资料

- [2rayN](https://github.com/freefq/free)
- [节点更新订阅](https://bulinkbulink.com/freefq/free/master/v2)