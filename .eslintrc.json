{"parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "prettier"], "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:react-hooks/recommended", "plugin:@typescript-eslint/recommended", "next/core-web-vitals", "plugin:prettier/recommended"], "rules": {"react/prop-types": "off", "react/react-in-jsx-scope": "off", "react/display-name": "off", "react/no-unescaped-entities": "off", "react/no-unknown-property": [2, {"ignore": ["jsx", "global"]}], "react-hooks/rules-of-hooks": "off", "react-hooks/exhaustive-deps": "off", "no-unused-vars": "off", "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_"}], "no-undef": "off", "@next/next/no-img-element": "off", "jsx-a11y/alt-text": "off", "prettier/prettier": ["error", {}, {"usePrettierrc": true}]}, "settings": {"react": {"version": "detect"}}, "ignorePatterns": ["node_modules/", ".next/", "out/", "public/", "*.d.ts"]}