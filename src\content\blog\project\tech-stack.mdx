---
title: 技术栈
description: 项目所采用的技术栈及其优势
date: 2025-04-06
category: 开发
tags:
  - 网站开发
  - 技术栈
---

在选择技术栈时，充分考虑了项目的需求、性能、可维护性和开发效率以后 ，最终选择了以下技术栈：

## 域名

域名是在阿里云上购买的，转移到了 Cloudflare 上做解析。大部分时候，阿里云买域名都比较便宜，而 Cloudflare 上的很多服务都是免费且量大管饱。

选择了 iflux.art 作为域名。没有选择 .com 后缀，当然是因为被抢注了。冷门后缀会也有好处，一是让域名看起来更短；二是在很多平台发，直接发域名，不会被判为站外引流。

## 基础库

使用了 React + react-dom（最新稳定版）。

React 是一个广泛使用的前端库，具有强大的社区支持和丰富的生态系统。它的组件化设计使得开发可复用的 UI 组件变得简单高效。

与 Vue.js 相比，React 提供了更大的灵活性和更强的生态系统，但学习曲线可能稍陡峭。

## 前端框架

使用了 Next.js + next-themes（App Router）。

Next.js 是一个基于 React 的框架，支持服务器端渲染（SSR）和静态生成（SSG），能够显著提高页面加载速度和 SEO 性能。与传统的 React 应用相比，Next.js 提供了更好的性能和开发体验。

使用 next-themes 可以轻松实现主题切换，提升用户体验。

App Router 是一个基于 Next.js 的路由库，支持动态路由、嵌套路由和路由参数。与传统的 Next.js 路由相比，App Router 提供了更灵活的路由配置和更好的开发体验。

## CSS 框架

使用了 Tailwind CSS v4 版本。

Tailwind CSS 是一个实用优先的 CSS 框架，允许开发者通过类名快速构建自定义设计。

与 Bootstrap 等传统框架相比，Tailwind 提供了更高的灵活性和可定制性，避免了样式冲突和冗余。

## 组件库

使用了 Radix UI + ShadcnUI 组件库。

Radix UI 提供了一组可访问的 UI 组件，确保我们的应用符合 WCAG 标准。

ShadcnUI 组件库则提供了丰富的组件选择，帮助我们快速构建用户界面。

与 Material-UI 相比，这些库在可访问性和灵活性上表现更佳。

## 图标库

使用了 lucide-react 。

lucide-react 提供了一致的图标风格，增强了用户界面的视觉一致性。

与 Font Awesome 等图标库相比，lucide-react 更加轻量且易于使用。

## 包管理器

使用了 pnpm 。

pnpm 是一个高效的包管理器，能够减少磁盘空间的使用并加快安装速度。

与 npm 和 yarn 相比，pnpm 在处理大型项目时表现更优。

## Node.js

选择最新的 Node.js 版本，可以确保我们使用最新的特性和性能优化，同时提高安全性。

## 部署工具

代码托管在 GitHub 上，通过 Vercel 部署。

GitHub 提供了强大的版本控制和协作功能，而 Vercel 则是一个专为 Next.js 优化的部署平台，能够快速部署静态网站和服务器端渲染应用。

与传统的云服务相比，Vercel 提供了更简单的配置和更快的部署速度。