{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "allowSyntheticDefaultImports": true, "noImplicitAny": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "plugins": [{"name": "next"}], "baseUrl": "./", "paths": {"@/*": ["./src/*"]}}, "include": ["src/types/global.d.ts", "next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "node_modules/@types/**/*.d.ts", "node_modules/@vercel/analytics/**/*.ts", "node_modules/@vercel/speed-insights/**/*.ts"], "exclude": ["node_modules", "!node_modules/@types"]}