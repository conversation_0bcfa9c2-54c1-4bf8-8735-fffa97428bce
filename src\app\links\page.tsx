'use client';

import React, { useMemo, useState } from 'react';
import {
  SAMPLE_LINKS,
  getAllCategories,
  filterLinksByCategory,
  filterLinksByTag,
} from '@/config/links';
import { UnifiedGrid } from '@/components/layout/unified-grid';
import { UnifiedCard } from '@/components/common/unified-card';
import { UnifiedFilter } from '@/components/common/filter/unified-filter';

// 生成带计数的标签数组
function getTagsWithCount(items: { tags: string[] }[]): { name: string; count: number }[] {
  const tagMap = new Map<string, number>();
  items.forEach(item => {
    item.tags.forEach(tag => {
      tagMap.set(tag, (tagMap.get(tag) || 0) + 1);
    });
  });
  return Array.from(tagMap.entries())
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => b.count - a.count);
}

interface LinksHeaderProps {
  totalItems: number;
  filteredCount: number;
  selectedCategory?: string;
  selectedTag?: string | null;
  getCategoryName: (categoryId: string) => string;
}

function LinksHeader({
  totalItems,
  filteredCount,
  selectedCategory,
  selectedTag,
  getCategoryName,
}: LinksHeaderProps) {
  return (
    <div className="mb-8">
      <h1 className="text-3xl font-bold tracking-tight mb-2">网址</h1>
      {selectedCategory || selectedTag ? (
        <p className="text-muted-foreground">
          显示 {filteredCount} 个网址
          {selectedCategory && ` · ${getCategoryName(selectedCategory)}`}
          {selectedTag && ` · ${selectedTag}`}
        </p>
      ) : (
        <p className="text-muted-foreground">
          共收录 {totalItems} 个优质网站，欢迎
          <a
            href="https://ocnzi0a8y98s.feishu.cn/share/base/form/shrcnB0sog9RdZVM8FLJNXVsFFb"
            target="_blank"
            rel="noreferrer"
          >
            互换友链
          </a>
        </p>
      )}
    </div>
  );
}

export default function LinksPage() {
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedTag, setSelectedTag] = useState<string | null>(null);

  const categories = useMemo(() => getAllCategories(), []);

  // 先按分类筛选，再按标签筛选
  const filteredByCategory = useMemo(() => {
    if (!selectedCategory || selectedCategory === '') return SAMPLE_LINKS;
    return filterLinksByCategory(SAMPLE_LINKS, selectedCategory);
  }, [selectedCategory]);

  const filteredItems = useMemo(() => {
    if (!selectedTag) return filteredByCategory;
    return filterLinksByTag(filteredByCategory, selectedTag);
  }, [filteredByCategory, selectedTag]);

  // 标签只统计当前分类下的
  const filteredTags = useMemo(() => getTagsWithCount(filteredByCategory), [filteredByCategory]);

  // 分类名称获取函数
  const getCategoryName = (categoryId: string) => {
    const cat = categories.find(c => c.id === categoryId);
    return cat ? cat.name : categoryId;
  };

  // 分类切换时清空标签
  const handleCategoryChange = (catId: string) => {
    setSelectedCategory(catId);
    setSelectedTag(null);
  };

  if (!SAMPLE_LINKS.length) return null;
  return (
    <div className="container mx-auto px-4 py-8">
      <LinksHeader
        filteredCount={filteredItems.length}
        selectedCategory={selectedCategory}
        selectedTag={selectedTag}
        totalItems={SAMPLE_LINKS.length}
        getCategoryName={getCategoryName}
      />

      <UnifiedFilter
        categories={categories.map(cat => ({
          id: cat.id,
          name: cat.name,
          icon: undefined,
        }))}
        selectedCategory={selectedCategory}
        onCategoryChange={handleCategoryChange}
        tags={filteredTags}
        selectedTag={selectedTag}
        onTagChange={setSelectedTag}
        onCardTagClick={setSelectedTag}
        categoryButtonClassName="rounded-full"
        className="mb-6"
      />

      <UnifiedGrid columns={5} className="items-stretch">
        {filteredItems.map(item => (
          <UnifiedCard
            key={item.id}
            type="category"
            variant="compact"
            title={item.title}
            description={item.description || item.url}
            href={item.url}
            icon={item.icon}
            iconType={item.iconType}
            isExternal={true}
            tags={item.tags}
            onTagClick={setSelectedTag}
            className="h-full"
          />
        ))}
      </UnifiedGrid>
    </div>
  );
}
