/**
 * MDX 插件配置
 * 统一管理所有 MDX 插件的配置，避免重复定义
 */

import remarkGfm from 'remark-gfm';
import rehypeSlug from 'rehype-slug';
import rehypeAutolinkHeadings from 'rehype-autolink-headings';
import rehypePrettyCode from 'rehype-pretty-code';

// ==================== 主题配置 ====================

export const SHIKI_THEMES = {
  light: 'github-light',
  dark: 'github-dark',
} as const;

// ==================== Rehype Pretty Code 配置 ====================

export const REHYPE_PRETTY_CODE_CONFIG = {
  // 支持浅色和深色主题
  theme: {
    light: SHIKI_THEMES.light,
    dark: SHIKI_THEMES.dark,
  },
  keepBackground: false,
  // 默认语言
  defaultLang: 'plaintext',
  // 确保空行显示正确
  onVisitLine(node: any) {
    if (node.children.length === 0) {
      node.children = [{ type: 'text', value: ' ' }];
    }
  },
  // 高亮行样式
  onVisitHighlightedLine(node: any) {
    if (!node.properties.className) {
      node.properties.className = [];
    }
    node.properties.className.push('line--highlighted');
  },
  // 高亮词样式
  onVisitHighlightedWord(node: any) {
    node.properties.className = ['word--highlighted'];
  },
  // 过滤元数据字符串
  filterMetaString: (string: string) => string.replace(/filename="[^"]*"/, ''),
  // 确保代码高亮生效的转换器
  transformers: [
    {
      name: 'preserve-syntax-highlighting',
      pre(node: any) {
        // 添加 shiki 类名以确保样式应用
        if (!node.properties.className) {
          node.properties.className = [];
        }
        if (Array.isArray(node.properties.className)) {
          node.properties.className.push('shiki');
        }

        // 保留 Shiki 生成的语法高亮样式
        if (node.properties && node.properties.style) {
          // 只移除背景色，保留其他样式
          const style = node.properties.style;
          if (typeof style === 'string') {
            node.properties.style = style.replace(/background-color:[^;]+;?/g, '');
          }
        }
      },
      code(node: any) {
        // 添加 shiki 类名
        if (!node.properties.className) {
          node.properties.className = [];
        }
        if (Array.isArray(node.properties.className)) {
          node.properties.className.push('shiki');
        }

        // 确保 code 元素保留语法高亮
        if (node.properties && node.properties.style) {
          // 保留所有语法高亮样式
          return;
        }
      },
      span(node: any) {
        // 确保 span 元素保留语法高亮样式
        if (node.properties && node.properties.style) {
          // 保留所有内联样式
          return;
        }
      },
    },
  ],
} as const;

// ==================== Rehype Autolink Headings 配置 ====================

export const REHYPE_AUTOLINK_HEADINGS_CONFIG = {
  behavior: 'wrap' as const,
  properties: {
    className: ['anchor'],
    ariaLabel: 'Link to section',
  },
  content: {
    type: 'element',
    tagName: 'span',
    properties: {
      className: ['anchor-icon'],
      ariaHidden: true,
    },
    children: [{ type: 'text', value: '#' }],
  },
} as const;

// ==================== 插件配置 ====================

/**
 * Remark 插件配置
 * 用于处理 Markdown 语法扩展
 */
export const REMARK_PLUGINS = [
  remarkGfm, // GitHub Flavored Markdown 支持
] as const;

/**
 * Rehype 插件配置
 * 用于处理 HTML 转换和增强
 */
export const REHYPE_PLUGINS = [
  rehypeSlug, // 为标题添加 id
  [rehypeAutolinkHeadings, REHYPE_AUTOLINK_HEADINGS_CONFIG], // 为标题添加锚点链接
  [rehypePrettyCode, REHYPE_PRETTY_CODE_CONFIG], // 代码语法高亮
] as const;

// ==================== MDX 编译选项 ====================

/**
 * MDX 编译选项
 * 用于 @next/mdx 和 next-mdx-remote
 */
export const MDX_COMPILE_OPTIONS = {
  remarkPlugins: REMARK_PLUGINS,
  rehypePlugins: REHYPE_PLUGINS,
  jsx: true,
  format: 'mdx' as const,
} as const;

// ==================== Next.js MDX 配置 ====================

/**
 * Next.js MDX 配置
 * 用于 next.config.mjs 中的 @next/mdx
 */
export const NEXT_MDX_CONFIG = {
  options: MDX_COMPILE_OPTIONS,
} as const;

// ==================== MDX Remote 配置 ====================

/**
 * MDX Remote 序列化配置
 * 用于 next-mdx-remote 的 serialize 函数
 */
export const MDX_SERIALIZE_OPTIONS = {
  mdxOptions: MDX_COMPILE_OPTIONS,
  parseFrontmatter: true,
} as const;

// ==================== 开发环境配置 ====================

/**
 * 开发环境特定配置
 */
export const DEV_MDX_CONFIG = {
  ...MDX_COMPILE_OPTIONS,
  development: process.env.NODE_ENV === 'development',
} as const;

// ==================== 生产环境配置 ====================

/**
 * 生产环境特定配置
 */
export const PROD_MDX_CONFIG = {
  ...MDX_COMPILE_OPTIONS,
  development: false,
} as const;

// ==================== 环境配置选择 ====================

/**
 * 根据环境选择配置
 */
export const ENV_MDX_CONFIG =
  process.env.NODE_ENV === 'production' ? PROD_MDX_CONFIG : DEV_MDX_CONFIG;

// ==================== 导出默认配置 ====================

export default {
  themes: SHIKI_THEMES,
  plugins: {
    remark: REMARK_PLUGINS,
    rehype: REHYPE_PLUGINS,
  },
  compile: MDX_COMPILE_OPTIONS,
  serialize: MDX_SERIALIZE_OPTIONS,
  next: NEXT_MDX_CONFIG,
  env: ENV_MDX_CONFIG,
} as const;
