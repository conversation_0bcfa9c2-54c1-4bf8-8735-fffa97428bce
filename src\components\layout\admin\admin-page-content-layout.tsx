import React from 'react';
import { LucideIcon } from 'lucide-react';
import Link from 'next/link';
import { cn } from '@/utils';

interface AdminPageContentLayoutProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  icon?: LucideIcon;
  backUrl?: string;
  backLabel?: string;
  className?: string;
  headerClassName?: string;
  contentClassName?: string;
}

export default function AdminPageContentLayout({
  children,
  title,
  description,
  icon: Icon,
  backUrl,
  backLabel,
  className,
  headerClassName,
  contentClassName,
}: AdminPageContentLayoutProps) {
  return (
    <div
      className={cn('min-h-screen p-4 sm:p-6 md:p-8', className)}
      role="region"
      aria-label="管理页面内容"
    >
      <div className="mx-auto max-w-7xl">
        {(title || description || backUrl) && (
          <header
            className={cn(
              'flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-6 sm:mb-8',
              headerClassName
            )}
          >
            {(title || description) && (
              <div>
                {title && (
                  <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">{title}</h1>
                )}
                {description && (
                  <p className="mt-1 text-sm sm:text-base text-muted-foreground">{description}</p>
                )}
              </div>
            )}
            {(backUrl || Icon) && (
              <div className="flex items-center space-x-3">
                {Icon && (
                  <Icon
                    className="h-5 w-5 sm:h-6 sm:w-6 text-muted-foreground"
                    aria-hidden="true"
                  />
                )}
                {backUrl && (
                  <Link
                    href={backUrl}
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                    aria-label={backLabel || '返回'}
                  >
                    {backLabel || '返回'}
                  </Link>
                )}
              </div>
            )}
          </header>
        )}
        <main className={cn('w-full', contentClassName)} role="main">
          {children}
        </main>
      </div>
    </div>
  );
}
