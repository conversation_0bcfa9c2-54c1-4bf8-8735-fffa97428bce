'use client';

import { ThemeProvider } from 'next-themes';
import { ServiceWorkerProvider } from '@/components/providers/service-worker-provider';
import React, { useEffect } from 'react';
import { initializeStores } from '@/stores';

const themeConfig = {
  attribute: 'class',
  defaultTheme: 'system',
  enableSystem: true,
  storageKey: 'iflux-theme-preference',
  disableTransitionOnChange: true,
} as const;

export function Providers({ children }: { children: React.ReactNode }) {
  // 初始化 Zustand 状态管理
  useEffect(() => {
    initializeStores();
  }, []);

  return (
    <ThemeProvider {...themeConfig}>
      <ServiceWorkerProvider />
      {children}
    </ThemeProvider>
  );
}
