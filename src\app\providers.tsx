'use client';

import { ThemeProvider } from 'next-themes';
import { ServiceWorkerProvider } from '@/components/providers/service-worker-provider';
import React, { useEffect } from 'react';
import { useAppStore } from '@/stores/app-store';

const themeConfig = {
  attribute: 'class',
  defaultTheme: 'system',
  enableSystem: true,
  storageKey: 'iflux-theme-preference',
  disableTransitionOnChange: true,
} as const;

export function Providers({ children }: { children: React.ReactNode }) {
  // 初始化 Zustand 状态管理（仅在客户端）
  useEffect(() => {
    // 手动触发 persist 状态的水合
    useAppStore.persist.rehydrate();
  }, []);

  return (
    <ThemeProvider {...themeConfig}>
      <ServiceWorkerProvider />
      {children}
    </ThemeProvider>
  );
}
