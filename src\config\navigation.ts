import { Home, Globe, type LucideIcon } from 'lucide-react';

/**
 * 基础导航项接口
 */
interface BaseNavItem {
  /** 导航项唯一标识 */
  key: string;
  /** 显示名称 */
  label: string;
  /** 描述文本 */
  description: string;
  /** 是否在特定场景下隐藏 */
  hidden?: boolean;
}

/**
 * 管理菜单项接口
 */
interface AdminNavItem extends BaseNavItem {
  /** 图标组件 */
  icon: LucideIcon;
  /** 路径 */
  href: string;
}

/**
 * 主导航菜单项
 */
export const NAV_ITEMS: BaseNavItem[] = [
  {
    key: 'blog',
    label: '博客',
    description: '阅读最新的博客文章，了解行业动态和技术趋势',
  },
  {
    key: 'docs',
    label: '文档',
    description: '查看详细的文档和指南，了解如何使用我们的产品和服务',
  },
  {
    key: 'journal',
    label: '日志',
    description: '浏览时间轴格式的文章和文档更新记录',
  },
  {
    key: 'tools',
    label: '工具',
    description: '发现实用的在线工具，提升工作和学习效率',
  },
  {
    key: 'links',
    label: '导航',
    description: '发现精选的网站和工具，提高您的工作效率',
  },
];

/**
 * 导航路径映射
 */
export const NAV_PATHS: Record<string, string> = {
  blog: '/blog',
  docs: '/docs',
  journal: '/journal',
  tools: '/tools',
  links: '/links',
};

/**
 * 导航描述映射
 */
export const NAV_DESCRIPTIONS: Record<string, string> = {
  blog: NAV_ITEMS.find(item => item.key === 'blog')!.description,
  docs: NAV_ITEMS.find(item => item.key === 'docs')!.description,
  journal: NAV_ITEMS.find(item => item.key === 'journal')!.description,
  tools: NAV_ITEMS.find(item => item.key === 'tools')!.description,
  links: NAV_ITEMS.find(item => item.key === 'links')!.description,
};

/**
 * 管理后台导航菜单
 */
export const ADMIN_NAV_ITEMS: AdminNavItem[] = [
  {
    key: 'dashboard',
    label: '仪表板',
    description: '系统概览和统计信息',
    icon: Home,
    href: '/admin',
  },
  {
    key: 'links',
    label: '链接管理',
    description: '管理导航链接',
    icon: Globe,
    href: '/admin/links',
  },
];

/**
 * 管理菜单配置（向后兼容）
 * @deprecated 请使用 ADMIN_NAV_ITEMS
 */
export const ADMIN_MENU_ITEMS = ADMIN_NAV_ITEMS;

/**
 * 获取导航项的完整信息
 */
export function getNavItem(key: string): BaseNavItem | undefined {
  return NAV_ITEMS.find(item => item.key === key);
}

/**
 * 获取导航项的路径
 */
export function getNavPath(key: string): string {
  return NAV_PATHS[key] || '/';
}

/**
 * 获取导航项的描述
 */
export function getNavDescription(key: string): string {
  return NAV_DESCRIPTIONS[key] || '';
}

/**
 * 检查导航配置完整性
 */
if (process.env.NODE_ENV === 'development') {
  const navKeys = NAV_ITEMS.map(item => item.key);
  const pathKeys = Object.keys(NAV_PATHS);
  const descKeys = Object.keys(NAV_DESCRIPTIONS);

  // 确保所有配置项的key一致
  const allKeysMatch = navKeys.every(key => pathKeys.includes(key) && descKeys.includes(key));

  if (!allKeysMatch) {
    console.warn('Navigation configuration mismatch detected!');
    console.warn('NAV_ITEMS keys:', navKeys);
    console.warn('NAV_PATHS keys:', pathKeys);
    console.warn('NAV_DESCRIPTIONS keys:', descKeys);
  }
}

/**
 * 导航配置常量
 */
export const NAVIGATION_CONFIG = {
  /** 主导航项数量 */
  MAX_MAIN_NAV_ITEMS: 6,
  /** 是否显示管理入口 */
  SHOW_ADMIN_ENTRY: process.env.NODE_ENV === 'development',
  /** 默认激活的导航项 */
  DEFAULT_ACTIVE_NAV: 'blog',
} as const;
