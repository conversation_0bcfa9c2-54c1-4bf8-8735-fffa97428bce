'use client';

import React, { useState, useCallback } from 'react';
import { Copy, Check } from 'lucide-react';
import { cn } from '@/utils';

export interface MDXCodeInlineProps extends React.HTMLAttributes<HTMLElement> {
  /** 代码内容 */
  children: React.ReactNode;
  /** 自定义类名 */
  className?: string;
  /** 是否为行内代码 */
  inline?: boolean;
  /** 代码语言 */
  language?: string;
  /** 样式变体 */
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  /** 是否可复制 */
  copyable?: boolean;
}

// 样式变体映射
const variantStyles = {
  default: '',
  primary: 'text-primary bg-primary/10',
  secondary: 'text-secondary bg-secondary/10',
  success: 'text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-950',
  warning: 'text-amber-600 bg-amber-50 dark:text-amber-400 dark:bg-amber-950',
  error: 'text-red-600 bg-red-50 dark:text-red-400 dark:bg-red-950',
} as const;

/**
 * MDX 行内代码组件
 * - 支持多种样式变体
 * - 可复制功能
 * - 响应式设计
 * - 暗色模式支持
 * - 优化的性能和可访问性
 */
export const MDXCodeInline = React.forwardRef<HTMLElement, MDXCodeInlineProps>(
  ({ children, className, variant = 'default', copyable = false, ...props }, ref) => {
    const [copied, setCopied] = useState(false);

    // 处理内容，移除多余的引号
    const processContent = useCallback((content: React.ReactNode): React.ReactNode => {
      if (typeof content === 'string') {
        if (content.startsWith("'") && content.endsWith("'")) {
          return content.slice(1, -1);
        }
      }
      return content;
    }, []);

    // 复制到剪贴板
    const handleCopy = useCallback(async () => {
      const text = typeof children === 'string' ? children : String(children);
      try {
        await navigator.clipboard.writeText(text);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (error) {
        console.error('Failed to copy:', error);
      }
    }, [children]);

    const processedContent = processContent(children);

    const baseStyles = cn(
      // 基础样式
      'relative inline-flex items-center gap-1',
      'px-1.5 py-0.5 mx-0.5',
      'text-sm font-mono font-medium',
      'rounded-md border',
      'transition-all duration-200',

      // 默认颜色
      'bg-muted/50 text-muted-foreground',
      'border-border/50',

      // 悬停效果
      'hover:bg-muted/80 hover:text-foreground',
      'hover:border-border',

      // 变体样式
      variantStyles[variant],

      // 自定义类名
      className
    );

    if (copyable) {
      return (
        <span className="inline-flex items-center gap-1">
          <code ref={ref} className={baseStyles} {...props}>
            {processedContent}
          </code>
          <button
            type="button"
            onClick={handleCopy}
            className={cn(
              'inline-flex items-center justify-center',
              'w-4 h-4 p-0.5',
              'text-muted-foreground hover:text-foreground',
              'rounded transition-colors duration-200',
              'opacity-0 group-hover:opacity-100',
              'focus:opacity-100 focus:outline-none'
            )}
            aria-label={copied ? 'Copied!' : 'Copy code'}
          >
            {copied ? <Check className="w-3 h-3" /> : <Copy className="w-3 h-3" />}
          </button>
        </span>
      );
    }

    return (
      <code ref={ref} className={baseStyles} {...props}>
        {processedContent}
      </code>
    );
  }
);

MDXCodeInline.displayName = 'MDXCodeInline';

// 样式变体组件
export const PrimaryCode = (props: Omit<MDXCodeInlineProps, 'variant'>) => (
  <MDXCodeInline variant="primary" {...props} />
);

export const SecondaryCode = (props: Omit<MDXCodeInlineProps, 'variant'>) => (
  <MDXCodeInline variant="secondary" {...props} />
);

export const SuccessCode = (props: Omit<MDXCodeInlineProps, 'variant'>) => (
  <MDXCodeInline variant="success" {...props} />
);

export const WarningCode = (props: Omit<MDXCodeInlineProps, 'variant'>) => (
  <MDXCodeInline variant="warning" {...props} />
);

export const ErrorCode = (props: Omit<MDXCodeInlineProps, 'variant'>) => (
  <MDXCodeInline variant="error" {...props} />
);

export default MDXCodeInline;
