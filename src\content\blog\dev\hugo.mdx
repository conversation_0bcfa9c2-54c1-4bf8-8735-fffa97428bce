---
title: Hugo 静态网站开发教程
description: 从零开始搭建 Hugo 静态网站，包括环境配置、主题定制、内容管理、性能优化和自动化部署等全方位实践指南
date: "2024-09-23"
category: "前端开发"
tags:
- Hugo
- Go语言
- 静态站点
- 主题开发
- 性能优化
- 自动部署
---

## 创建仓库

Hugo 的部分主题（如 [Stack](https://github.com/CaiJimmy/hugo-theme-stack)），在 GitHub 上提供了快速入门模板（quick start template），只需几步，即可轻松创建一个个人网站（博客）。

### 新建存储库

在 [Hugo Theme Stack Starter Template](https://github.com/CaiJimmy/hugo-theme-stack-starter) 主页，点击使用这个模板（Use this template），新建一个存储库（repository）。

如果之后要绑定自己的独立域名，存储库可以随便填；如果没有自己的域名，就命名为 `<username>.github.io`（ username 即你的 GitHub 用户名，不区分大小写）。

### 修改分支

在 GitHub 上打开 `Settings` -> `Pages` ，将存储库的分支 `Branch` 从 `master` 更改为 `gh-pages` 。

这样一来，我们的主题和网站就可以分开了，我们的设置操作在 `master` ，主题更新在 `gh-pages` ，互相不受影响。

## 本地部署

### 前置安装软件

在本地运行 Hugo ，需要安装 [Git](https://git-scm.com/book/en/v2/Getting-Started-Installing-Git) 和 [Go](https://go.dev/doc/install)，分别在官网下载后安装即可。

### 克隆网站源码

打开刚才创建的存储库主页，找到 `Code` 按钮，任选 `HTTPS` 、`SSH` 、`GitHub CLI` 中的一个链接复制；在想要创建的位置，通过命令行的方式，输入以下命令，把我们刚才创建的网站源码，克隆到喜欢的位置：

```bash
<NAME_EMAIL>:用户名/存储库名.git
```

> 用户名和存储库名要修改成自己的；  
> 也可以选择下载压缩包后解压，效果是一样的；  
> 下载的库自带文件夹，不需要创建新的。

### 下载 Hugo 扩展

根据自己的系统类型，在 hugo 的 [Releases](https://github.com/gohugoio/hugo/releases) 页面，下载适合的版本。

> 推荐下载名称中带 `extended` 的压缩包。
> 64 位的 Windows 系统，下载 `hugo_extended_0.134.2_windows-amd64.zip` 即可（具体版本号以实际为准）。

下载压缩包后解压，不需要双击安装，直接将 `hugo.exe` 复制到网站源码的根目录下。

右键打开终端，输入以下命令后，将结果中的网址（一般为：`http://localhost:1313/`）复制到浏览器，即可看到网站的效果。

```bash
.\hugo server
```

## 网站基础设置

修改 `config/module.toml` 文件即可。

## 主题美化

美化操作是在 `assets` -> `scss` -> `custom.scss` 文件中。

如果要手动更新主题，依次运行以下命令即可：

```bash
hugo mod get -u github.com/CaiJimmy/hugo-theme-stack/v3
```

```bash
hugo mod tidy
```