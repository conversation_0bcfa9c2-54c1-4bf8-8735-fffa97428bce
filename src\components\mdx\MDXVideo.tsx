'use client';

import React, { useState, useCallback, useMemo, forwardRef } from 'react';
import { Play, ExternalLink, AlertCircle } from 'lucide-react';
import { cn } from '@/utils';

export interface MDXVideoProps {
  /** 视频源地址或视频网站URL */
  src: string;
  /** 视频标题 */
  title?: string;
  /** 海报图片 */
  poster?: string;
  /** 是否自动播放 */
  autoPlay?: boolean;
  /** 是否显示控件 */
  controls?: boolean;
  /** 是否循环播放 */
  loop?: boolean;
  /** 是否静音 */
  muted?: boolean;
  /** 自定义类名 */
  className?: string;
  /** 宽度（默认100%） */
  width?: number | string;
  /** 高度（自适应） */
  height?: number | string;
  /** 宽高比（默认16:9） */
  aspectRatio?: string;
  /** 是否启用懒加载 */
  lazy?: boolean;
  /** 错误回调 */
  onError?: (error: string) => void;
}

/**
 * 视频平台配置
 */
interface VideoPlatform {
  name: string;
  icon: string;
  embedUrl: (id: string) => string;
  regex: RegExp;
  extractId: (url: string) => string | null;
}

/**
 * 支持的视频平台配置
 */
const VIDEO_PLATFORMS: VideoPlatform[] = [
  {
    name: 'YouTube',
    icon: '📺',
    embedUrl: (id: string) => `https://www.youtube.com/embed/${id}`,
    regex:
      /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/,
    extractId: (url: string) => {
      const match = url.match(
        /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/
      );
      return match ? match[1] : null;
    },
  },
  {
    name: 'Bilibili',
    icon: '📱',
    embedUrl: (id: string) => `https://player.bilibili.com/player.html?bvid=${id}&page=1`,
    regex: /bilibili\.com\/video\/(BV[a-zA-Z0-9]+)/,
    extractId: (url: string) => {
      const match = url.match(/bilibili\.com\/video\/(BV[a-zA-Z0-9]+)/);
      return match ? match[1] : null;
    },
  },
  {
    name: 'Vimeo',
    icon: '🎬',
    embedUrl: (id: string) => `https://player.vimeo.com/video/${id}`,
    regex: /vimeo\.com\/(\d+)/,
    extractId: (url: string) => {
      const match = url.match(/vimeo\.com\/(\d+)/);
      return match ? match[1] : null;
    },
  },
];

/**
 * 检测视频类型和平台
 */
const detectVideoType = (src: string) => {
  // 检查是否为视频平台链接
  for (const platform of VIDEO_PLATFORMS) {
    const id = platform.extractId(src);
    if (id) {
      return {
        type: 'embed' as const,
        platform,
        id,
        embedUrl: platform.embedUrl(id),
      };
    }
  }

  // 检查是否为直接视频文件
  const videoExtensions = /\.(mp4|webm|ogg|avi|mov|wmv|flv|mkv)(\?.*)?$/i;
  if (videoExtensions.test(src)) {
    return {
      type: 'direct' as const,
      platform: null,
      id: null,
      embedUrl: src,
    };
  }

  return null;
};

/**
 * MDX 视频播放器组件
 * - 支持 YouTube、Bilibili、Vimeo 等主流视频网站
 * - 支持直接视频文件播放
 * - 响应式设计，自适应不同设备
 * - 懒加载支持
 * - 可访问性支持
 */
export const MDXVideo = forwardRef<HTMLDivElement, MDXVideoProps>(
  (
    {
      src,
      title,
      poster,
      autoPlay = false,
      controls = true,
      loop = false,
      muted = false,
      className,
      width = '100%',
      height,
      aspectRatio = '16/9',
      lazy = true,
      onError,
      ...props
    },
    ref
  ) => {
    const [isLoaded, setIsLoaded] = useState(!lazy);
    const [hasError, setHasError] = useState(false);

    // 检测视频类型
    const videoInfo = useMemo(() => detectVideoType(src), [src]);

    // 处理加载
    const handleLoad = useCallback(() => {
      setIsLoaded(true);
    }, []);

    // 处理错误
    const handleError = useCallback(
      (error: string) => {
        setHasError(true);
        onError?.(error);
      },
      [onError]
    );

    // 如果检测失败，显示错误
    if (!videoInfo) {
      return (
        <div
          ref={ref}
          className={cn(
            'my-6 flex items-center justify-center',
            'rounded-lg border border-destructive/20',
            'bg-destructive/5 p-8 text-center',
            className
          )}
          {...props}
        >
          <div className="flex flex-col items-center gap-3">
            <AlertCircle className="h-8 w-8 text-destructive" />
            <div>
              <p className="font-medium text-destructive">不支持的视频格式</p>
              <p className="text-sm text-muted-foreground mt-1">
                请使用支持的视频文件格式或视频网站链接
              </p>
            </div>
          </div>
        </div>
      );
    }

    // 错误状态
    if (hasError) {
      return (
        <div
          ref={ref}
          className={cn(
            'my-6 flex items-center justify-center',
            'rounded-lg border border-destructive/20',
            'bg-destructive/5 p-8 text-center',
            className
          )}
          {...props}
        >
          <div className="flex flex-col items-center gap-3">
            <AlertCircle className="h-8 w-8 text-destructive" />
            <div>
              <p className="font-medium text-destructive">视频加载失败</p>
              <p className="text-sm text-muted-foreground mt-1">请检查视频链接是否正确</p>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div
        ref={ref}
        className={cn(
          'relative my-6 overflow-hidden rounded-lg',
          'bg-card border border-border',
          'shadow-sm group',
          className
        )}
        style={{
          width,
          aspectRatio: height ? undefined : aspectRatio,
        }}
        {...props}
      >
        {/* 懒加载占位符 */}
        {!isLoaded && (
          <div
            className={cn(
              'absolute inset-0 flex items-center justify-center',
              'bg-muted cursor-pointer',
              'transition-all duration-200 hover:bg-muted/80'
            )}
            onClick={handleLoad}
          >
            <div className="flex flex-col items-center gap-3">
              <div className="flex items-center gap-2">
                {videoInfo.platform?.icon && (
                  <span className="text-2xl" role="img" aria-label={videoInfo.platform.name}>
                    {videoInfo.platform.icon}
                  </span>
                )}
                <Play className="h-8 w-8 text-primary" />
              </div>
              <div className="text-center">
                <p className="font-medium text-foreground">
                  {title || `播放${videoInfo.platform?.name || ''}视频`}
                </p>
                <p className="text-sm text-muted-foreground mt-1">点击加载视频</p>
              </div>
            </div>
          </div>
        )}

        {/* 视频内容 */}
        {isLoaded && (
          <>
            {videoInfo.type === 'embed' ? (
              // 嵌入式视频（YouTube、Bilibili 等）
              <iframe
                src={videoInfo.embedUrl}
                title={title || `${videoInfo.platform?.name} 视频`}
                className="w-full h-full border-0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
                loading="lazy"
                onError={() => handleError('嵌入视频加载失败')}
              />
            ) : (
              // 直接视频文件
              <video
                src={videoInfo.embedUrl}
                poster={poster}
                controls={controls}
                autoPlay={autoPlay}
                loop={loop}
                muted={muted}
                playsInline
                className="w-full h-full"
                onError={() => handleError('视频文件加载失败')}
              />
            )}

            {/* 视频信息覆盖层 */}
            {(title || videoInfo.platform) && (
              <div
                className={cn(
                  'absolute top-0 left-0 right-0',
                  'flex items-center justify-between',
                  'px-4 py-3',
                  'bg-gradient-to-b from-black/60 to-transparent',
                  'opacity-0 group-hover:opacity-100',
                  'transition-opacity duration-200'
                )}
              >
                <div className="flex items-center gap-2">
                  {videoInfo.platform?.icon && (
                    <span className="text-sm" role="img" aria-label={videoInfo.platform.name}>
                      {videoInfo.platform.icon}
                    </span>
                  )}
                  <h3 className="text-white text-sm font-medium">
                    {title || `${videoInfo.platform?.name} 视频`}
                  </h3>
                </div>

                {videoInfo.type === 'embed' && (
                  <a
                    href={src}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={cn(
                      'flex items-center gap-1 px-2 py-1',
                      'text-xs text-white/80 hover:text-white',
                      'bg-black/20 hover:bg-black/40',
                      'rounded transition-colors'
                    )}
                    aria-label="在新窗口中打开视频"
                  >
                    <ExternalLink className="h-3 w-3" />
                    <span>原站观看</span>
                  </a>
                )}
              </div>
            )}
          </>
        )}
      </div>
    );
  }
);

MDXVideo.displayName = 'MDXVideo';
