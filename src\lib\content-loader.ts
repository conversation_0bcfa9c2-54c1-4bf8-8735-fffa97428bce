import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';
import { z } from 'zod';
import { cache } from 'react';
import { safeParse, validateOrThrow } from '@/schemas';

// ==================== 常量定义 ====================

const CONTENT_DIR = path.join(process.cwd(), 'src/content');

// ==================== 辅助函数 ====================

/**
 * 安全地读取文件内容
 * @param filePath - 文件的绝对路径
 * @returns 文件内容，如果文件不存在则返回 null
 */
const safeReadFile = (filePath: string): string | null => {
  try {
    return fs.readFileSync(filePath, 'utf-8');
  } catch (error) {
    if (error instanceof Error && 'code' in error && error.code === 'ENOENT') {
      return null;
    }
    throw error;
  }
};

// ==================== 核心加载器 ====================

/**
 * 通用的内容加载和校验函数
 *
 * @param contentType - 内容类型 ('blog' or 'docs')
 * @param slugParts - URL slug 数组
 * @param schema - 用于校验 Frontmatter 的 Zod Schema
 * @returns 返回包含校验后 frontmatter 和原始内容的对象，或在找不到文件或校验失败时返回 null
 */
export const loadContent = cache(
  <T extends z.ZodTypeAny>(
    contentType: 'blog' | 'docs',
    slugParts: string[],
    schema: T
  ): { frontmatter: z.infer<T>; content: string } | null => {
    const slug = slugParts.join('/');
    const filePath = path.join(CONTENT_DIR, contentType, `${slug}.mdx`);
    const fileContent = safeReadFile(filePath);

    if (!fileContent) {
      console.warn(`Content file not found at: ${filePath}`);
      return null;
    }

    try {
      const { data, content } = matter(fileContent);
      const result = safeParse(schema, data);

      if (!result.success) {
        console.error(`Frontmatter validation failed for ${filePath}:`, result.error.errors);
        // 在生产环境中，校验失败应该被视为严重错误
        if (process.env.NODE_ENV === 'production') {
          throw new Error(`Invalid content: ${filePath}`);
        }
        return null;
      }

      return {
        frontmatter: result.data,
        content,
      };
    } catch (error) {
      console.error(`Error parsing frontmatter for ${filePath}:`, error);
      return null;
    }
  }
);
