/**
 * 管理员界面配置
 * 统一管理表格配置、操作按钮等管理员相关配置
 */

import { Plus, Edit, Trash2, ExternalLink, Search } from 'lucide-react';

// ==================== 表格配置 ====================

/**
 * 表格列配置（不包含 render 函数，在组件中实现）
 */
export const LINKS_TABLE_COLUMNS = [
  {
    key: 'icon',
    title: '图标',
    width: '64px',
  },
  {
    key: 'title',
    title: '标题',
    width: '450px',
  },
  {
    key: 'category',
    title: '分类',
    width: '120px',
  },
  {
    key: 'tags',
    title: '标签',
    width: '350px',
  },
  {
    key: 'featured',
    title: '特色',
    width: '80px',
  },
  {
    key: 'createdAt',
    title: '创建时间',
    width: '120px',
  },
] as const;

/**
 * 表格操作按钮配置
 */
export const getTableActions = () => [
  {
    key: 'view',
    label: '查看',
    icon: ExternalLink,
    variant: 'ghost' as const,
    size: 'sm' as const,
  },
  {
    key: 'edit',
    label: '编辑',
    icon: Edit,
    variant: 'ghost' as const,
    size: 'sm' as const,
  },
  {
    key: 'delete',
    label: '删除',
    icon: Trash2,
    variant: 'ghost' as const,
    size: 'sm' as const,
    className: 'text-destructive hover:text-destructive',
  },
];

/**
 * 表格工具栏配置
 */
export const getTableToolbar = () => [
  {
    key: 'add',
    label: '添加链接',
    icon: Plus,
    variant: 'default' as const,
    size: 'sm' as const,
  },
  {
    key: 'search',
    label: '搜索',
    icon: Search,
    variant: 'outline' as const,
    size: 'sm' as const,
  },
];

// ==================== 表单配置 ====================

/**
 * 链接表单字段配置
 */
export const LINK_FORM_FIELDS = {
  title: {
    label: '标题',
    placeholder: '请输入链接标题',
    required: true,
    maxLength: 100,
  },
  description: {
    label: '描述',
    placeholder: '请输入链接描述',
    required: false,
    maxLength: 200,
  },
  url: {
    label: 'URL',
    placeholder: 'https://example.com',
    required: true,
    type: 'url',
  },
  category: {
    label: '分类',
    placeholder: '请选择分类',
    required: true,
  },
  tags: {
    label: '标签',
    placeholder: '请输入标签，按回车添加',
    required: false,
    maxTags: 10,
  },
  icon: {
    label: '图标',
    placeholder: '请输入图标或选择图片',
    required: false,
  },
  iconType: {
    label: '图标类型',
    options: [
      { value: 'emoji', label: 'Emoji' },
      { value: 'image', label: '图片' },
    ],
    default: 'emoji',
  },
  featured: {
    label: '设为特色',
    description: '特色链接会在首页显示',
    default: false,
  },
} as const;

// ==================== 管理员配置 ====================

/**
 * 管理员界面配置
 */
export const ADMIN_CONFIG = {
  /** 页面标题 */
  title: '链接管理',
  /** 页面描述 */
  description: '管理网站导航链接',
  /** 每页显示数量 */
  pageSize: 20,
  /** 支持的页面大小选项 */
  pageSizeOptions: [10, 20, 50, 100],
  /** 是否显示页面大小选择器 */
  showSizeChanger: true,
  /** 是否显示快速跳转 */
  showQuickJumper: true,
  /** 表格是否可选择 */
  rowSelection: true,
  /** 表格是否可排序 */
  sortable: true,
  /** 表格是否可筛选 */
  filterable: true,
  /** 是否显示表格边框 */
  bordered: false,
  /** 表格大小 */
  size: 'middle' as const,
} as const;

/**
 * 操作确认配置
 */
export const CONFIRM_CONFIG = {
  delete: {
    title: '确认删除',
    content: '确定要删除这个链接吗？此操作不可撤销。',
    okText: '删除',
    cancelText: '取消',
    type: 'warning' as const,
  },
  batchDelete: {
    title: '批量删除',
    content: '确定要删除选中的链接吗？此操作不可撤销。',
    okText: '删除',
    cancelText: '取消',
    type: 'warning' as const,
  },
} as const;

/**
 * 消息提示配置
 */
export const MESSAGE_CONFIG = {
  success: {
    add: '链接添加成功',
    edit: '链接更新成功',
    delete: '链接删除成功',
    batchDelete: '批量删除成功',
  },
  error: {
    add: '链接添加失败',
    edit: '链接更新失败',
    delete: '链接删除失败',
    batchDelete: '批量删除失败',
    network: '网络错误，请稍后重试',
    validation: '表单验证失败，请检查输入',
  },
  loading: {
    add: '正在添加链接...',
    edit: '正在更新链接...',
    delete: '正在删除链接...',
    fetch: '正在加载数据...',
    parse: '正在解析网站信息...',
  },
} as const;

// ==================== 验证规则 ====================

/**
 * 表单验证规则
 */
export const VALIDATION_RULES = {
  title: {
    required: true,
    minLength: 1,
    maxLength: 100,
    pattern: /^.+$/,
    message: '标题长度应在1-100个字符之间',
  },
  description: {
    required: false,
    maxLength: 200,
    message: '描述长度不能超过200个字符',
  },
  url: {
    required: true,
    pattern: /^https?:\/\/.+/,
    message: '请输入有效的URL地址',
  },
  category: {
    required: true,
    message: '请选择分类',
  },
  tags: {
    maxItems: 10,
    maxLength: 20,
    message: '标签数量不能超过10个，每个标签长度不能超过20个字符',
  },
} as const;

// ==================== 快捷操作 ====================

/**
 * 快捷操作配置
 */
export const QUICK_ACTIONS = {
  /** 快速添加常用网站 */
  quickAdd: [
    { name: 'GitHub', url: 'https://github.com', category: 'development' },
    { name: 'ChatGPT', url: 'https://chat.openai.com', category: 'ai' },
    { name: 'Figma', url: 'https://figma.com', category: 'design' },
    { name: 'Notion', url: 'https://notion.so', category: 'productivity' },
  ],
  /** 批量操作 */
  batchActions: [
    { key: 'delete', label: '批量删除', icon: Trash2, variant: 'destructive' as const },
    { key: 'export', label: '导出数据', icon: ExternalLink, variant: 'outline' as const },
  ],
} as const;

// ==================== 类型导出 ====================

export type AdminConfig = typeof ADMIN_CONFIG;
export type ConfirmConfig = typeof CONFIRM_CONFIG;
export type MessageConfig = typeof MESSAGE_CONFIG;
export type ValidationRules = typeof VALIDATION_RULES;
export type QuickActions = typeof QUICK_ACTIONS;
