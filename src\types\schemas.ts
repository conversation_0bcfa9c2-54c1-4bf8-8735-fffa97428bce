import { z } from 'zod';

// ==================== 基础 Schema ====================

/**
 * 基础 Frontmatter Schema
 * 包含所有内容类型共有的字段
 */
export const baseFrontmatterSchema = z.object({
  title: z.string({ required_error: '标题是必填项' }).min(1, '标题不能为空'),
  description: z.string().optional(),
  date: z
    .union([z.string().regex(/^\d{4}-\d{2}-\d{2}$/, '日期格式必须是 YYYY-MM-DD'), z.date()])
    .optional(),
  lastModified: z
    .union([z.string().regex(/^\d{4}-\d{2}-\d{2}$/, '日期格式必须是 YYYY-MM-DD'), z.date()])
    .optional(),
  tags: z.array(z.string()).optional(),
  draft: z.boolean().default(false).optional(),
  image: z.string().url('图片必须是有效的 URL').optional(),
});

// ==================== 博客文章 Schema ====================

/**
 * 博客文章 Frontmatter Schema
 */
export const blogFrontmatterSchema = baseFrontmatterSchema.extend({
  category: z.string().optional(),
  author: z.string().optional(),
});

/**
 * 博客文章的完整数据结构 Schema (Frontmatter + content)
 */
export const blogPostSchema = z.object({
  frontmatter: blogFrontmatterSchema,
  content: z.string(),
  slug: z.string(),
  readingTime: z.number().optional(),
});

// ==================== 文档页面 Schema ====================

/**
 * 文档页面 Frontmatter Schema
 */
export const docsFrontmatterSchema = baseFrontmatterSchema.extend({
  sidebarTitle: z.string().optional(),
  order: z.number().optional(),
});

/**
 * 文档页面的完整数据结构 Schema (Frontmatter + content)
 */
export const docPageSchema = z.object({
  frontmatter: docsFrontmatterSchema,
  content: z.string(),
  slug: z.string(),
});

// ==================== 类型推断 ====================

export type BaseFrontmatter = z.infer<typeof baseFrontmatterSchema>;
export type BlogFrontmatter = z.infer<typeof blogFrontmatterSchema>;
export type BlogPost = z.infer<typeof blogPostSchema>;
export type DocsFrontmatter = z.infer<typeof docsFrontmatterSchema>;
export type DocPage = z.infer<typeof docPageSchema>;
