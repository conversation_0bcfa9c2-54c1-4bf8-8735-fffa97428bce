/**
 * Zustand 状态管理统一导出
 * 提供所有状态存储和相关工具的统一入口
 */

// ==================== 类型导出 ====================
export type {
  // 基础状态类型
  ThemeState,
  AuthState,
  SearchState,
  NavbarState,
  ToolState,
  MDXState,
  FilterState,
  CacheState,

  // 组合状态类型
  AppState,
  ToolPageState,

  // 数据类型
  ZustandSearchResult,
  ToolHistoryItem,
  CacheItem,

  // 配置类型
  PersistConfig,
  StateSliceConfig,
  StateSetter,
  StateGetter,
  StateSubscriber,
  StateStore,
} from './types';

// ==================== 配置导出 ====================
export {
  // 存储配置
  DEFAULT_STORAGE_CONFIG,
  SESSION_STORAGE_CONFIG,

  // 持久化配置
  THEME_PERSIST_CONFIG,
  AUTH_PERSIST_CONFIG,
  SEARCH_PERSIST_CONFIG,
  TOOL_PERSIST_CONFIG,
  CACHE_PERSIST_CONFIG,

  // 状态切片配置
  THEME_SLICE_CONFIG,
  AUTH_SLICE_CONFIG,
  SEARCH_SLICE_CONFIG,
  NAVBAR_SLICE_CONFIG,
  TOOL_SLICE_CONFIG,
  FILTER_SLICE_CONFIG,
  CACHE_SLICE_CONFIG,

  // 工具函数
  createPersistMiddleware,
  createStateSlice,
  createResetFunction,
  validateState,

  // 常量
  STATE_CONFIG,
  STORAGE_KEYS,
} from './config';

// ==================== 主状态存储导出 ====================
export {
  // 主状态存储
  useAppStore,

  // 状态选择器
  useTheme,
  useAuth,
  useSearch,
  useNavbar,
  useFilter,
  useCache,
} from './app-store';

// ==================== 工具状态存储导出 ====================
export {
  // 工具状态存储
  useToolStore,

  // 工具状态选择器
  useToolState,
  useToolHistory,
  useToolPage,

  // 工具特定 Hooks
  useCalculatorState,
  useBase64State,
  useTextCounterState,
  useRegexTesterState,
  useTimestampConverterState,
} from './tool-store';

// ==================== 状态管理工具函数 ====================

/**
 * 初始化状态管理
 */
export function initializeStores() {
  // 这里可以添加状态初始化逻辑
  console.log('Zustand stores initialized');
}

/**
 * 重置所有状态
 */
export function resetAllStores() {
  // 这里可以添加重置所有状态的逻辑
  console.log('All stores reset');
}

/**
 * 状态持久化工具
 */
export const storeUtils = {
  /**
   * 清除所有持久化数据
   */
  clearAllPersisted() {
    if (typeof window !== 'undefined') {
      const storageKeys = [
        'iflux-app-storage',
        'iflux-tool-storage',
        'iflux-theme-storage',
        'iflux-auth-storage',
        'iflux-search-storage',
        'iflux-cache-storage',
      ];
      storageKeys.forEach(key => {
        localStorage.removeItem(key);
        sessionStorage.removeItem(key);
      });
    }
  },

  /**
   * 获取存储大小
   */
  getStorageSize() {
    if (typeof window === 'undefined') return 0;

    const storageKeys = [
      'iflux-app-storage',
      'iflux-tool-storage',
      'iflux-theme-storage',
      'iflux-auth-storage',
      'iflux-search-storage',
      'iflux-cache-storage',
    ];

    let total = 0;
    storageKeys.forEach(key => {
      const item = localStorage.getItem(key);
      if (item) {
        total += item.length;
      }
    });
    return total;
  },

  /**
   * 导出状态数据
   */
  exportState() {
    if (typeof window === 'undefined') return null;

    const storageKeys = [
      'iflux-app-storage',
      'iflux-tool-storage',
      'iflux-theme-storage',
      'iflux-auth-storage',
      'iflux-search-storage',
      'iflux-cache-storage',
    ];

    const data: Record<string, any> = {};
    storageKeys.forEach(key => {
      const item = localStorage.getItem(key);
      if (item) {
        try {
          data[key] = JSON.parse(item);
        } catch (error) {
          console.error(`Failed to parse ${key}:`, error);
        }
      }
    });
    return data;
  },

  /**
   * 导入状态数据
   */
  importState(data: Record<string, any>) {
    if (typeof window === 'undefined') return;

    const validKeys = [
      'iflux-app-storage',
      'iflux-tool-storage',
      'iflux-theme-storage',
      'iflux-auth-storage',
      'iflux-search-storage',
      'iflux-cache-storage',
    ];

    Object.entries(data).forEach(([key, value]) => {
      if (validKeys.includes(key)) {
        try {
          localStorage.setItem(key, JSON.stringify(value));
        } catch (error) {
          console.error(`Failed to import ${key}:`, error);
        }
      }
    });
  },
};

// ==================== 开发工具 ====================

/**
 * 开发环境下的状态调试工具
 */
export const devTools = {
  /**
   * 打印当前状态
   */
  logCurrentState() {
    if (process.env.NODE_ENV === 'development') {
      console.log('Zustand stores are active');
    }
  },

  /**
   * 监听状态变化
   */
  subscribeToChanges() {
    if (process.env.NODE_ENV === 'development') {
      console.log('State change monitoring enabled');
    }
  },
};

// ==================== 默认导出 ====================
export default {
  // 工具
  storeUtils,
  devTools,
  initializeStores,
  resetAllStores,
};
