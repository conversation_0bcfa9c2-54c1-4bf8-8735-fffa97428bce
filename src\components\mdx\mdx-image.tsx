'use client';

import React from 'react';
import Image, { type ImageProps } from 'next/image';
import { cn } from '@/utils';

export interface MDXImageProps extends Omit<ImageProps, 'src' | 'alt'> {
  /** 图片源地址 */
  src: string;
  /** 图片描述文本 */
  alt?: string;
  /** 图片说明文字 */
  caption?: React.ReactNode;
  /** 是否优先加载 */
  priority?: boolean;
  /** 图片宽度 */
  width?: number;
  /** 图片高度 */
  height?: number;
}

/**
 * 图片样式配置
 */
const imageStyles = {
  wrapper: cn('my-6 w-full', 'inline-flex flex-col items-center', 'not-prose'),

  image: cn(
    'rounded-xl',
    'shadow-md dark:shadow-none',
    'border border-border/50',
    'transition-all duration-300',
    'hover:shadow-lg hover:scale-[1.02]',
    'max-w-full h-auto'
  ),

  caption: cn('mt-3 text-sm text-muted-foreground', 'text-center italic', 'max-w-prose'),
};

/**
 * MDX 图片组件
 *
 * 功能：
 * 1. 响应式图片显示
 * 2. 自动优化和懒加载
 * 3. 支持图片说明文字
 * 4. 悬停效果和过渡动画
 * 5. 主题适配
 */
export const MDXImage = React.forwardRef<HTMLImageElement, MDXImageProps>(
  ({ src, alt = '', caption, className, priority = false, width, height, ...props }, ref) => {
    // 处理图片尺寸
    const imageProps = {
      src,
      alt,
      priority,
      className: cn(imageStyles.image, className),
      ...props,
    };

    // 如果提供了宽高，使用固定尺寸
    if (width && height) {
      Object.assign(imageProps, { width, height });
    } else {
      // 否则使用填充模式
      Object.assign(imageProps, {
        fill: true,
        sizes: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
      });
    }

    return (
      <figure className={imageStyles.wrapper}>
        <div className="relative w-full">
          <Image ref={ref} {...imageProps} />
        </div>
        {caption && <figcaption className={imageStyles.caption}>{caption}</figcaption>}
      </figure>
    );
  }
);

MDXImage.displayName = 'MDXImage';

export default MDXImage;
