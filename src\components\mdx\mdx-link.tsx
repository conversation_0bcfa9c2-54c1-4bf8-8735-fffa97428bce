'use client';

import React from 'react';
import Link from 'next/link';
import { ExternalLink } from 'lucide-react';
import { cn } from '@/utils';

export interface MDXLinkProps extends React.AnchorHTMLAttributes<HTMLAnchorElement> {
  /** 链接地址 */
  href?: string;
  /** 链接文本 */
  children: React.ReactNode;
  /** 是否为外部链接 */
  external?: boolean;
  /** 是否显示外部链接图标 */
  showIcon?: boolean;
  /** 自定义类名 */
  className?: string;
}

/**
 * 检查是否为外部链接
 */
const isExternalLink = (href: string): boolean => {
  return href.startsWith('http') || href.startsWith('//');
};

/**
 * 检查是否为邮箱链接
 */
const isEmailLink = (href: string): boolean => {
  return href.startsWith('mailto:');
};

/**
 * 检查是否为电话链接
 */
const isTelLink = (href: string): boolean => {
  return href.startsWith('tel:');
};

/**
 * 链接样式配置
 */
const linkStyles = {
  base: cn(
    'relative inline-flex items-center gap-1',
    'text-primary hover:text-primary/80',
    'transition-colors duration-200',
    'no-underline',
    // 下划线动画效果
    'after:absolute after:left-1/2 after:bottom-0',
    'after:h-px after:w-0 after:bg-current',
    'after:transition-all after:duration-300',
    'hover:after:left-0 hover:after:w-full'
  ),

  external: cn('cursor-alias'),

  icon: cn('h-3 w-3 flex-shrink-0', 'opacity-70'),
};

/**
 * MDX 链接组件
 *
 * 功能：
 * 1. 自动识别内部/外部链接
 * 2. 外部链接自动添加图标
 * 3. 支持邮箱和电话链接
 * 4. 优雅的悬停动画效果
 * 5. 无障碍访问支持
 */
export const MDXLink = React.forwardRef<HTMLAnchorElement, MDXLinkProps>(
  ({ href = '#', children, external, showIcon = true, className, ...props }, ref) => {
    // 自动检测链接类型
    const isExternal = external ?? isExternalLink(href);
    const isEmail = isEmailLink(href);
    const isTel = isTelLink(href);

    // 构建样式
    const linkClassName = cn(linkStyles.base, isExternal && linkStyles.external, className);

    // 外部链接或特殊链接使用原生 a 标签
    if (isExternal || isEmail || isTel) {
      return (
        <a
          ref={ref}
          href={href}
          className={linkClassName}
          target={isExternal ? '_blank' : undefined}
          rel={isExternal ? 'noopener noreferrer' : undefined}
          {...props}
        >
          {children}
          {isExternal && showIcon && (
            <ExternalLink className={linkStyles.icon} aria-hidden="true" />
          )}
        </a>
      );
    }

    // 内部链接使用 Next.js Link
    return (
      <Link ref={ref} href={href} className={linkClassName} {...props}>
        {children}
      </Link>
    );
  }
);

MDXLink.displayName = 'MDXLink';

export default MDXLink;
