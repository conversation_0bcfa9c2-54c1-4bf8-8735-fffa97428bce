'use client';

import React from 'react';
import { cn } from '@/utils';

export interface MDXHeadingProps extends React.HTMLAttributes<HTMLHeadingElement> {
  /** 标题级别 */
  level?: 1 | 2 | 3 | 4 | 5 | 6;
  /** 标题文本 */
  children: React.ReactNode;
  /** 自定义 ID */
  id?: string;
  /** 自定义类名 */
  className?: string;
}

/**
 * 生成标题 ID
 */
const generateHeadingId = (text: string): string => {
  return text
    .toLowerCase()
    .replace(/\s+/g, '-')
    .replace(/[^\w-]/g, '')
    .replace(/--+/g, '-')
    .replace(/^-|-$/g, '');
};

/**
 * 提取文本内容
 */
const extractTextContent = (children: React.ReactNode): string => {
  if (typeof children === 'string') {
    return children;
  }

  if (React.isValidElement(children)) {
    const props = children.props as { children?: React.ReactNode };
    if (props?.children) {
      return extractTextContent(props.children);
    }
  }

  if (Array.isArray(children)) {
    return children.map(extractTextContent).join('');
  }

  return String(children || '');
};

/**
 * 标题样式配置
 */
const headingStyles = {
  1: 'text-4xl font-bold tracking-tight lg:text-5xl mb-8',
  2: 'text-3xl font-semibold tracking-tight mb-6 mt-12 first:mt-0',
  3: 'text-2xl font-semibold tracking-tight mb-4 mt-8',
  4: 'text-xl font-semibold tracking-tight mb-3 mt-6',
  5: 'text-lg font-semibold tracking-tight mb-2 mt-4',
  6: 'text-base font-semibold tracking-tight mb-2 mt-4',
} as const;

/**
 * MDX 标题组件
 *
 * 功能：
 * 1. 自动生成标题 ID
 * 2. 支持锚点链接
 * 3. 响应式设计
 * 4. 主题适配
 */
export const MDXHeading = React.forwardRef<HTMLHeadingElement, MDXHeadingProps>(
  ({ level = 2, children, id, className, ...props }, ref) => {
    // 提取文本内容用于生成 ID
    const textContent = extractTextContent(children);

    // 生成或使用提供的 ID
    const headingId = id || generateHeadingId(textContent);

    // 选择标题标签
    const HeadingTag = `h${level}` as 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';

    // 构建样式
    const headingClassName = cn(
      // 基础样式
      'scroll-mt-20', // 为锚点滚动预留空间
      'group relative',

      // 级别样式
      headingStyles[level],

      // 自定义样式
      className
    );

    return (
      <HeadingTag ref={ref} id={headingId} className={headingClassName} {...props}>
        {children}

        {/* 锚点链接 */}
        {level >= 2 && (
          <a
            href={`#${headingId}`}
            className={cn(
              'absolute -left-6 top-0 flex h-full items-center',
              'text-muted-foreground hover:text-foreground',
              'opacity-0 group-hover:opacity-100',
              'transition-opacity duration-200',
              'no-underline'
            )}
            aria-label={`Link to ${textContent}`}
            onClick={e => {
              e.preventDefault();
              // 复制链接到剪贴板
              const url = new URL(window.location.href);
              url.hash = headingId;
              navigator.clipboard?.writeText(url.toString());

              // 滚动到标题
              const element = document.getElementById(headingId);
              if (element) {
                element.scrollIntoView({
                  behavior: 'smooth',
                  block: 'start',
                });
                // 更新 URL
                history.replaceState(null, '', `#${headingId}`);
              }
            }}
          >
            <span className="text-lg font-normal">#</span>
          </a>
        )}
      </HeadingTag>
    );
  }
);

MDXHeading.displayName = 'MDXHeading';

// 导出不同级别的标题组件
export const H1 = (props: Omit<MDXHeadingProps, 'level'>) => <MDXHeading level={1} {...props} />;

export const H2 = (props: Omit<MDXHeadingProps, 'level'>) => <MDXHeading level={2} {...props} />;

export const H3 = (props: Omit<MDXHeadingProps, 'level'>) => <MDXHeading level={3} {...props} />;

export const H4 = (props: Omit<MDXHeadingProps, 'level'>) => <MDXHeading level={4} {...props} />;

export const H5 = (props: Omit<MDXHeadingProps, 'level'>) => <MDXHeading level={5} {...props} />;

export const H6 = (props: Omit<MDXHeadingProps, 'level'>) => <MDXHeading level={6} {...props} />;

export default MDXHeading;
