/**
 * 应用主状态存储
 * 使用 Zustand 管理全局应用状态
 */

import { create } from 'zustand';
import { persist, subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import type { AppState, ZustandSearchResult, CacheItem } from './types';
import { STATE_CONFIG } from './config';

/**
 * 应用主状态存储
 */
export const useAppStore = create<AppState>()(
  subscribeWithSelector(
    persist(
      immer((set, get) => ({
        // ==================== 初始化状态 ====================
        initialized: false,
        setInitialized: (initialized: boolean) => {
          set(state => {
            state.initialized = initialized;
          });
        },

        // ==================== 主题状态 ====================
        theme: 'system' as const,
        setTheme: (theme: 'light' | 'dark' | 'system') => {
          set(state => {
            state.theme = theme;
          });
        },

        // ==================== 认证状态 ====================
        isLoggedIn: false,
        loginTime: null,
        setLoginState: (isLoggedIn: boolean, loginTime?: number) => {
          set(state => {
            state.isLoggedIn = isLoggedIn;
            state.loginTime = loginTime || Date.now();
          });
        },
        logout: () => {
          set(state => {
            state.isLoggedIn = false;
            state.loginTime = null;
          });
        },
        checkLoginExpiry: () => {
          const { isLoggedIn, loginTime } = get();
          if (!isLoggedIn || !loginTime) return false;

          const isExpired = Date.now() - loginTime > STATE_CONFIG.LOGIN_TIMEOUT;
          if (isExpired) {
            get().logout();
            return false;
          }
          return true;
        },

        // ==================== 搜索状态 ====================
        isOpen: false,
        query: '',
        results: [],
        isLoading: false,
        history: [],
        selectedIndex: 0,
        setOpen: (open: boolean) => {
          set(state => {
            state.isOpen = open;
            if (!open) {
              state.query = '';
              state.results = [];
              state.selectedIndex = 0;
            }
          });
        },
        setQuery: (query: string) => {
          set(state => {
            state.query = query;
            state.selectedIndex = 0;
          });
        },
        setResults: (results: ZustandSearchResult[]) => {
          set(state => {
            state.results = results;
            state.selectedIndex = 0;
          });
        },
        setLoading: (loading: boolean) => {
          set(state => {
            state.isLoading = loading;
          });
        },
        addToHistory: (query: string) => {
          if (!query.trim()) return;
          set(state => {
            const newHistory = [query, ...state.history.filter(item => item !== query)].slice(
              0,
              STATE_CONFIG.MAX_SEARCH_HISTORY
            );
            state.history = newHistory;
          });
        },
        clearHistory: () => {
          set(state => {
            state.history = [];
          });
        },
        setSelectedIndex: (index: number) => {
          set(state => {
            state.selectedIndex = Math.max(0, Math.min(index, state.results.length - 1));
          });
        },
        resetSearch: () => {
          set(state => {
            state.query = '';
            state.results = [];
            state.isLoading = false;
            state.selectedIndex = 0;
          });
        },

        // ==================== 导航栏状态 ====================
        direction: 'up' as const,
        position: 0,
        showTitle: false,
        pageTitle: '',
        lastDirectionChange: 0,
        setScrollPosition: (position: number) => {
          set(state => {
            const now = Date.now();
            const newDirection = position > state.position ? 'down' : 'up';
            const directionChanged = newDirection !== state.direction;

            // 首页始终显示导航菜单
            if (state.pageTitle === '首页') {
              state.direction = newDirection;
              state.position = position;
              state.showTitle = false;
              state.lastDirectionChange = directionChanged ? now : state.lastDirectionChange;
              return;
            }

            // 其他页面的逻辑
            if (Math.abs(position - state.position) <= STATE_CONFIG.SCROLL_THRESHOLD) {
              return;
            }

            state.direction = newDirection;
            state.position = position;
            state.lastDirectionChange = directionChanged ? now : state.lastDirectionChange;

            // 根据滚动方向和位置决定是否显示标题
            if (newDirection === 'down' && position > STATE_CONFIG.HIDE_THRESHOLD) {
              state.showTitle = true;
            } else if (newDirection === 'up' && position < STATE_CONFIG.SHOW_THRESHOLD) {
              state.showTitle = false;
            }
          });
        },
        setPageTitle: (title: string) => {
          set(state => {
            state.pageTitle = title;
          });
        },
        scrollToTop: () => {
          if (typeof window !== 'undefined') {
            window.scrollTo({ top: 0, behavior: 'smooth' });
          }
        },

        // ==================== 过滤状态 ====================
        selectedCategory: '',
        selectedTag: null,
        searchTerm: '',
        setCategory: (category: string) => {
          set(state => {
            state.selectedCategory = category;
          });
        },
        setTag: (tag: string | null) => {
          set(state => {
            state.selectedTag = tag;
          });
        },
        setSearchTerm: (term: string) => {
          set(state => {
            state.searchTerm = term;
          });
        },
        reset: () => {
          set(state => {
            state.selectedCategory = '';
            state.selectedTag = null;
            state.searchTerm = '';
          });
        },

        // ==================== 缓存状态 ====================
        cache: new Map<string, CacheItem>(),
        set: (key: string, value: unknown, ttl: number = STATE_CONFIG.CACHE_TTL) => {
          set(state => {
            state.cache.set(key, {
              value,
              timestamp: Date.now(),
              ttl,
            });
          });
        },
        get: (key: string) => {
          const { cache } = get();
          const item = cache.get(key);

          if (!item) return null;

          const isExpired = Date.now() - item.timestamp > item.ttl;
          if (isExpired) {
            get().remove(key);
            return null;
          }

          return item.value;
        },
        remove: (key: string) => {
          set(state => {
            state.cache.delete(key);
          });
        },
        clear: () => {
          set(state => {
            state.cache.clear();
          });
        },
        cleanup: () => {
          set(state => {
            const now = Date.now();
            for (const [key, item] of state.cache.entries()) {
              if (now - item.timestamp > item.ttl) {
                state.cache.delete(key);
              }
            }
          });
        },
      })),
      {
        name: 'iflux-app-storage',
        partialize: state => ({
          theme: state.theme,
          isLoggedIn: state.isLoggedIn,
          loginTime: state.loginTime,
          history: state.history,
        }),
        skipHydration: true, // 跳过水合，避免 SSR 问题
      }
    )
  )
);

// ==================== 状态选择器 ====================

/**
 * 主题状态选择器
 */
export const useTheme = () =>
  useAppStore(state => ({
    theme: state.theme,
    setTheme: state.setTheme,
  }));

/**
 * 认证状态选择器
 */
export const useAuth = () =>
  useAppStore(state => ({
    isLoggedIn: state.isLoggedIn,
    loginTime: state.loginTime,
    setLoginState: state.setLoginState,
    logout: state.logout,
    checkLoginExpiry: state.checkLoginExpiry,
  }));

/**
 * 搜索状态选择器
 */
export const useSearch = () =>
  useAppStore(state => ({
    isOpen: state.isOpen,
    query: state.query,
    results: state.results,
    isLoading: state.isLoading,
    history: state.history,
    selectedIndex: state.selectedIndex,
    setOpen: state.setOpen,
    setQuery: state.setQuery,
    setResults: state.setResults,
    setLoading: state.setLoading,
    addToHistory: state.addToHistory,
    clearHistory: state.clearHistory,
    setSelectedIndex: state.setSelectedIndex,
    resetSearch: state.resetSearch,
  }));

/**
 * 导航栏状态选择器
 */
export const useNavbar = () =>
  useAppStore(state => ({
    direction: state.direction,
    position: state.position,
    showTitle: state.showTitle,
    pageTitle: state.pageTitle,
    lastDirectionChange: state.lastDirectionChange,
    setScrollPosition: state.setScrollPosition,
    setPageTitle: state.setPageTitle,
    scrollToTop: state.scrollToTop,
  }));

/**
 * 过滤状态选择器
 */
export const useFilter = () =>
  useAppStore(state => ({
    selectedCategory: state.selectedCategory,
    selectedTag: state.selectedTag,
    searchTerm: state.searchTerm,
    setCategory: state.setCategory,
    setTag: state.setTag,
    setSearchTerm: state.setSearchTerm,
    reset: state.reset,
  }));

/**
 * 缓存状态选择器
 */
export const useCache = () =>
  useAppStore(state => ({
    set: state.set,
    get: state.get,
    remove: state.remove,
    clear: state.clear,
    cleanup: state.cleanup,
  }));
