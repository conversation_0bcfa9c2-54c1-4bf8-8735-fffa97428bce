---
title: Hexo 博客系统搭建指南
description: 全面介绍 Hexo 博客框架的核心功能和使用技巧，从环境搭建到主题定制，从内容创作到网站部署，打造个人专业博客
date: "2024-09-22"
category: "前端开发"
tags:
- Hexo
- 静态博客
- 主题开发
- Markdown
- 内容管理
- 网站部署
---

> Hexo 是一个快速、简洁且高效的博客框架。使用 Markdown（或其他标记语言）解析文章，在几秒内，即可利用靓丽的主题生成静态网页。

## 网站部署

### 确认系统类型

用的是 Windows、MacOS 还是 Linus？三大系统都支持搭建，只是操作上略有区别。

目前主流系统都采用 64 位，但有些老电脑还是 32 位，一般在系统设置中查看。

也可以通过命令行来确认。以 Windows 为例，在任意位置打开 “终端”，输入：

```bash
systeminfo
```

回车后，在“系统类型”中，如果显示的是 `x64-based PC`，就说明这是台 64 位系统的电脑。

### 安装必要软件

Hexo 的正常运行，需要先安装以下软件：

- [Git](https://git-scm.com/download/win)
- [Node.js](https://nodejs.org/zh-cn/download/)（版本不低于 10.13，官方建议 Node.js 12.0 及以上版本）

### 安装 Hexo

> 安装前，建议通读一遍 [Hexo官方文档](https://hexo.io/zh-cn/docs/)，对 Hexo 有个基本的了解。

以 Windows 为例，在你想要部署网站的文件夹（尽量是空文件夹）内，鼠标右键打开终端或 Git Bash，输入以下命令，下载依赖：

```bash
npm i hexo-cli -g
```

接着执行以下命令，对项目进行初始化：

```bash
hexo init
```

继续执行以下命令，完成 Hexo 的安装：

```bash
npm install
```

Hexo 就被部署到了这个文件夹里。

### 网站上传

#### 修改网站配置文件

打开网站根目录下的 `_config.yml` 文件，找到如下所示的 `Deployment` 板块。

```yml
# Deployment
## Docs: https://hexo.io/docs/deployment.html
deploy:
  type: ""
```

将其更改为：

```yml
# Deployment
## Docs: https://hexo.io/docs/deployment.html
deploy:
  type: "git"
  #这里填入你之前在GitHub上创建仓库的完整路径
  repo: **************:xxx/xxx.github.io.git
  branch: master
```

#### 安装依赖包

首次发布，建议在网站根目录下，用 Git Bash 输入以下代码：

```bash
npm install hexo-deployer-git --save
```

#### 生成静态文件（Generate static files）

输入以下命令：

```bash
hexo generate
```

可以简写为：

```bash
hexo g
```

#### 启动本地服务器（Run server）

输入以下命令：

```bash
hexo server
```

可以简写为：

```bash
hexo s
```

根据系统返回的提示，打开浏览器，输入 `localhost:4000` 回车后，即可看到你的网站。

#### 部署到远程站点（Deploy to remote sites）

继续输入以下命令，：

```bash
hexo deploy
```

可以简写为：

```bash
hexo d
```

或者输入以下命令生成静态页面，并发布至远程仓库（等同于 `hexo g` + `hexo d`）：

```bash
hexo d -g
```

在浏览器输入 `username.github.io`，就可以看到你的网站了。

>  将 `username` 替换为你的 GitHub 用户名。

以后有内容要更新，在本地编辑好以后，重复以上命令即可完成发布。如果以前的文章有删改，或者网站有较大的变动，建议先通过 `hexo clean` 清理缓存，然后通过 `hexo d -g` 发布。

## 网站设置

首先打开 Hexo 根目录下的 `_config.yml` 文件，修改 `Site` 板块为你自己的信息：

```yml
title: 网站标题
subtitle: "网站副标题"
description: "网站描述"
keywords: 关键词
author: 作者
language: zh-CN # 网站语言
timezone: "Asia/Shanghai" # 时区
```

## 主题设置

此时的网站还比较简陋，但好在 Hexo 有很多精品的模板资源，也就是主题（Theme），根据说明文档的提示，按需配置即可。

> 安装好的主题，一般在 `themes` 文件夹下，而通过 `npm` 安装，是在 `node_modules` 文件夹下。

我们只需要在网站根目录下找到 `_config.yml` 文件，打开后找到 `theme` 选项，将默认的 `landscape` 改为你安装的主题名即可。例如：

```yml
theme: anzhiyu
```

建议把主题根目录下的 `_config.yml` 文件复制一份，改名为 `_config.主题名称.yml` （如 `_config.solitude.yml` ）。把 `_config.主题名称.yml` 移动到网站根目录下。

在这个文件上的更改，优先级大于原来主题目录下的 `_config.yml` ，而且主题更新也不会被覆盖，万一出现自己解决不了的 bug，也随时可以删掉，再从主题目录复制一个过来。

切换主题后需要清理缓存，输入以下命令：

```bash
hexo clean
```

> 可简写为：`hexo cl`

然后在 Git Bash 中，分别输入以下 `hexo g` 和 `hexo s` 命令，便可在浏览器 ` localhost:4000` 页面中查看安装主题后的效果。