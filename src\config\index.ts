/**
 * 统一配置导出
 * 提供项目中所有配置的统一入口
 */

// ==================== 基础配置 ====================
export * from './site';
export * from './navigation';

// ==================== MDX 配置 ====================
export * from './mdx';

// ==================== 缓存配置 ====================
export * from './cache';

// ==================== 性能配置 ====================
export * from './performance';

// ==================== 默认导出 ====================
export { SITE_CONFIG as default } from './site';

// ==================== 配置验证 ====================
if (process.env.NODE_ENV === 'development') {
  // 在开发环境中验证配置完整性
  import('./site').then(({ SITE_CONFIG }) => {
    if (!SITE_CONFIG.name || !SITE_CONFIG.url) {
      console.warn('⚠️ 站点配置不完整，请检查 src/config/site.ts');
    }
  });

  import('./navigation').then(({ NAV_ITEMS, NAV_PATHS }) => {
    const navKeys = NAV_ITEMS.map(item => item.key);
    const pathKeys = Object.keys(NAV_PATHS);

    if (!navKeys.every(key => pathKeys.includes(key))) {
      console.warn('⚠️ 导航配置不匹配，请检查 src/config/navigation.ts');
    }
  });
}

/**
 * 配置文件组织说明：
 *
 * - site.ts: 站点基础信息配置
 * - navigation.ts: 导航菜单配置
 * - mdx/: MDX 相关配置
 * - cache.ts: 缓存策略配置
 *
 * 使用方式：
 * ```typescript
 * import { SITE_CONFIG, NAV_ITEMS } from '@/config';
 * // 或者
 * import { SITE_CONFIG } from '@/config/site';
 * ```
 */
