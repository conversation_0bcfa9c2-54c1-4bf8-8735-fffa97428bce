/**
 * 首页相关配置
 * 统一管理首页的各种配置数据、常量和AI模型
 */

import {
  Code,
  Palette,
  Lightbulb,
  Pencil,
  BookOpen,
  FileText,
  Compass,
  Github,
} from 'lucide-react';

// ==================== 类型定义 ====================

/**
 * 时间段类型
 */
export type TimeOfDay = 'morning' | 'afternoon' | 'evening' | 'night';

/**
 * 背景样式类型
 */
export type BackgroundStyle = 'wave';

/**
 * 搜索结果接口
 */
export interface SearchResult {
  title: string;
  content: string;
  url?: string;
  score?: number;
}

/**
 * AI模型接口
 */
export interface AIModel {
  id: string;
  name: string;
  provider: string;
  description: string;
  icon: string;
  color: string;
  apiUrl: string;
  apiKeyEnv: string;
  maxTokens: number;
  temperature: number;
  supportedModes: ('ai' | 'local' | 'web')[];
}

// ==================== 常量配置 ====================

/**
 * 根据时间段的问候语数据
 */
export const GREETINGS_BY_TIME: Record<TimeOfDay, string[]> = {
  morning: ['早安', '早上好', '早'],
  afternoon: ['午安', '下午好'],
  evening: ['晚安', '晚上好'],
  night: ['夜深了', '该休息了'],
};

/**
 * 推荐标签数据
 */
export const RECOMMENDATION_TAGS = {
  initial: [
    { icon: Code, text: '网页开发', href: '/docs/web-development' },
    { icon: Palette, text: '深入研究', href: '/docs/research' },
    { icon: Lightbulb, text: '项目模式', href: '/docs/project-patterns' },
    { icon: Pencil, text: '图像生成', href: '/docs/image-generation' },
  ],
  more: [
    { icon: BookOpen, text: '文档中心', href: '/docs' },
    { icon: FileText, text: '博客文章', href: '/blog' },
    { icon: Compass, text: '网址导航', href: '/navigation' },
    { icon: Github, text: 'GitHub', href: 'https://github.com/iflux-art/web' },
  ],
} as const;

/**
 * 背景样式选项
 */
export const BACKGROUND_STYLES = ['wave'] as const;

// ==================== 首页配置 ====================

/**
 * 首页基础配置
 */
export const HOME_CONFIG = {
  // 页面标题和描述
  title: '智能搜索与AI对话',
  subtitle: '探索知识，创造可能',
  description: '集成本地搜索、联网搜索和AI对话的智能平台',

  // 搜索配置
  search: {
    placeholder: '有什么可以帮助您的吗？',
    maxResults: 8,
    debounceDelay: 300,
    minQueryLength: 1,
  },

  // AI对话配置
  ai: {
    defaultModel: 'none',
    maxMessageHistory: 50,
    typingDelay: 50,
    maxTokens: 2000,
  },

  // 背景效果配置
  background: {
    defaultStyle: 'default',
    particleCount: 50,
    animationSpeed: 'slow',
    enableParallax: true,
  },

  // 动画配置
  animation: {
    fadeInDuration: 300,
    slideInDuration: 400,
    hoverTransition: 200,
    enableReducedMotion: true,
  },

  // 响应式断点
  breakpoints: {
    mobile: 768,
    tablet: 1024,
    desktop: 1280,
  },

  // 主题配置
  theme: {
    enableSystemTheme: true,
    defaultTheme: 'system',
    enableThemeTransition: true,
  },

  // 性能配置
  performance: {
    enableVirtualization: true,
    enableServiceWorker: false,
    cacheStrategy: 'networkFirst',
  },

  // 功能开关
  features: {
    enableLocalSearch: true,
    enableWebSearch: true,
    enableAIChat: true,
    enableVoiceInput: false,
    enableKeyboardShortcuts: true,
    enableAnalytics: false,
  },

  // 键盘快捷键
  shortcuts: {
    focusSearch: 'cmd+k',
    clearSearch: 'escape',
    submitSearch: 'enter',
    toggleTheme: 'cmd+shift+t',
  },

  // API配置
  api: {
    searchEndpoint: '/api/search',
    chatEndpoint: '/api/chat',
    timeout: 10000,
    retryAttempts: 3,
  },

  // 缓存配置
  cache: {
    searchResults: {
      ttl: 5 * 60 * 1000, // 5分钟
      maxSize: 100,
    },
    aiResponses: {
      ttl: 30 * 60 * 1000, // 30分钟
      maxSize: 50,
    },
  },
} as const;

// ==================== 环境配置 ====================

/**
 * 开发环境配置
 */
export const DEV_CONFIG = {
  enableDebugMode: process.env.NODE_ENV === 'development',
  showPerformanceMetrics: false,
  enableMockData: false,
  logLevel: 'info',
} as const;

/**
 * 生产环境配置
 */
export const PROD_CONFIG = {
  enableAnalytics: true,
  enableErrorReporting: true,
  enablePerformanceMonitoring: true,
  compressionLevel: 'high',
} as const;

/**
 * 根据环境选择配置
 */
export const ENV_CONFIG = process.env.NODE_ENV === 'production' ? PROD_CONFIG : DEV_CONFIG;

/**
 * 合并配置
 */
export const FINAL_CONFIG = {
  ...HOME_CONFIG,
  ...ENV_CONFIG,
} as const;

// ==================== AI模型配置 ====================

/**
 * AI模型列表
 */
export const AI_MODELS: AIModel[] = [
  {
    id: 'silicon-flow-qwen',
    name: 'Qwen (硅基流动)',
    provider: 'SiliconFlow',
    description: '通义千问模型，中文理解能力强',
    icon: 'Bot',
    color: 'from-cyan-500 to-blue-500',
    apiUrl: 'https://api.siliconflow.cn/v1/chat/completions',
    apiKeyEnv: 'SILICONFLOW_API_KEY',
    maxTokens: 2000,
    temperature: 0.7,
    supportedModes: ['ai', 'local', 'web'],
  },
  {
    id: 'silicon-flow-llama',
    name: 'Llama (硅基流动)',
    provider: 'SiliconFlow',
    description: 'Meta Llama 模型，开源强大',
    icon: 'Bot',
    color: 'from-orange-500 to-red-500',
    apiUrl: 'https://api.siliconflow.cn/v1/chat/completions',
    apiKeyEnv: 'SILICONFLOW_API_KEY',
    maxTokens: 2000,
    temperature: 0.7,
    supportedModes: ['ai', 'local', 'web'],
  },
  {
    id: 'openrouter-claude',
    name: 'Claude (OpenRouter)',
    provider: 'OpenRouter',
    description: 'Anthropic Claude 模型',
    icon: 'Bot',
    color: 'from-purple-500 to-pink-500',
    apiUrl: 'https://openrouter.ai/api/v1/chat/completions',
    apiKeyEnv: 'OPENROUTER_API_KEY',
    maxTokens: 2000,
    temperature: 0.7,
    supportedModes: ['ai', 'local', 'web'],
  },
  {
    id: 'openrouter-gemini',
    name: 'Gemini (OpenRouter)',
    provider: 'OpenRouter',
    description: 'Google Gemini 模型',
    icon: 'Bot',
    color: 'from-blue-500 to-green-500',
    apiUrl: 'https://openrouter.ai/api/v1/chat/completions',
    apiKeyEnv: 'OPENROUTER_API_KEY',
    maxTokens: 2000,
    temperature: 0.7,
    supportedModes: ['ai', 'local', 'web'],
  },
  {
    id: 'moonshot-v1',
    name: 'Moonshot (Kimi)',
    provider: 'Moonshot',
    description: 'Moonshot AI 大模型，长文本处理能力强',
    icon: 'Bot',
    color: 'from-indigo-500 to-purple-500',
    apiUrl: 'https://api.moonshot.cn/v1/chat/completions',
    apiKeyEnv: 'MOONSHOT_API_KEY',
    maxTokens: 2000,
    temperature: 0.7,
    supportedModes: ['ai', 'local', 'web'],
  },
  {
    id: 'zhipu-glm',
    name: 'GLM (智谱AI)',
    provider: 'ZhipuAI',
    description: '智谱AI GLM 模型，中文能力优秀',
    icon: 'Bot',
    color: 'from-teal-500 to-blue-500',
    apiUrl: 'https://open.bigmodel.cn/api/paas/v4/chat/completions',
    apiKeyEnv: 'ZHIPU_API_KEY',
    maxTokens: 2000,
    temperature: 0.7,
    supportedModes: ['ai', 'local', 'web'],
  },
  {
    id: 'baichuan-chat',
    name: 'Baichuan (百川)',
    provider: 'Baichuan',
    description: '百川智能大模型，中文理解优秀',
    icon: 'Bot',
    color: 'from-green-600 to-blue-600',
    apiUrl: 'https://api.baichuan-ai.com/v1/chat/completions',
    apiKeyEnv: 'BAICHUAN_API_KEY',
    maxTokens: 2000,
    temperature: 0.7,
    supportedModes: ['ai', 'local', 'web'],
  },
  {
    id: 'minimax-chat',
    name: 'MiniMax',
    provider: 'MiniMax',
    description: 'MiniMax 大模型，多模态能力强',
    icon: 'Bot',
    color: 'from-yellow-500 to-orange-500',
    apiUrl: 'https://api.minimax.chat/v1/text/chatcompletion_v2',
    apiKeyEnv: 'MINIMAX_API_KEY',
    maxTokens: 2000,
    temperature: 0.7,
    supportedModes: ['ai', 'local', 'web'],
  },
];

// ==================== 工具函数 ====================

/**
 * 获取默认模型
 */
export const getDefaultModel = (): AIModel => {
  return AI_MODELS[0];
};

/**
 * 根据ID获取模型
 */
export const getModelById = (id: string): AIModel | undefined => {
  return AI_MODELS.find(model => model.id === id);
};

/**
 * 获取可用的模型（检查环境变量）
 */
export const getAvailableModels = (): AIModel[] => {
  return AI_MODELS.filter(model => {
    const apiKey = process.env[model.apiKeyEnv];
    return apiKey && apiKey !== `your_${model.apiKeyEnv.toLowerCase()}_here`;
  });
};

/**
 * 生成演示回复的函数
 */
export const generateDemoResponse = (
  message: string,
  searchMode: string,
  searchResults?: SearchResult[],
  modelName: string = 'AI'
): string => {
  const responses = {
    ai: [
      `关于"${message}"，这是一个很有趣的问题。根据我的理解，我可以从以下几个方面来分析...`,
      `针对您提到的"${message}"，我建议可以从这些角度来思考：首先是基础概念的理解，其次是实际应用场景...`,
      `您的问题"${message}"涉及到多个层面。让我为您详细解答一下相关的要点和建议...`,
    ],
    local: [
      `基于本地搜索结果，关于"${message}"的问题，我找到了以下相关内容：\n\n${
        searchResults?.map((r, i) => `${i + 1}. ${r.title}: ${r.content}`).join('\n') ||
        '暂无相关结果'
      }\n\n结合这些信息，我建议...`,
      `根据本地文档和工具的搜索结果，"${message}"相关的内容如下：\n\n${
        searchResults?.map(r => `• ${r.title} - ${r.content}`).join('\n') || '未找到相关内容'
      }\n\n基于这些资源，您可以...`,
    ],
    web: [
      `通过网络搜索"${message}"，我找到了以下相关信息：\n\n${
        searchResults
          ?.map((r, i) => `${i + 1}. ${r.title}\n   ${r.content}\n   来源：${r.url}`)
          .join('\n\n') || '暂无网络搜索结果'
      }\n\n综合这些网络资源，我的建议是...`,
      `基于网络搜索结果，关于"${message}"的最新信息如下：\n\n${
        searchResults?.map(r => `• ${r.title} (${r.url})\n  ${r.content}`).join('\n\n') ||
        '未找到相关网络内容'
      }\n\n根据这些信息，您可以进一步...`,
    ],
  };

  const modeResponses = responses[searchMode as keyof typeof responses] || responses.ai;
  const randomResponse = modeResponses[Math.floor(Math.random() * modeResponses.length)];

  return `🤖 **${modelName} 演示模式回复**\n\n${randomResponse}\n\n---\n💡 *这是演示回复。要获得真实的AI回答，请配置有效的API密钥。*`;
};

// ==================== 类型导出 ====================

export type HomeConfig = typeof HOME_CONFIG;
export type DevConfig = typeof DEV_CONFIG;
export type ProdConfig = typeof PROD_CONFIG;
export type FinalConfig = typeof FINAL_CONFIG;
