'use client';

import { useToolState as useZustandToolState } from '@/stores';

/**
 * 工具状态管理 Hook
 *
 * 使用 Zustand 状态管理，提供工具页面的状态和操作
 *
 * @deprecated 使用 useToolState from '@/stores' 替代
 */
export function useToolState() {
  return useZustandToolState();
}

  const clearAll = useCallback(() => {
    setState(prev => ({
      ...prev,
      input: '',
      output: '',
      error: null,
    }));
  }, []);

  const reset = useCallback(() => {
    setState({
      input: '',
      output: '',
      loading: false,
      error: null,
    });
  }, []);

  const actions: ToolActions = {
    setInput,
    setOutput,
    setError,
    setLoading,
    clearAll,
    reset,
  };

  return [state, actions];
}
