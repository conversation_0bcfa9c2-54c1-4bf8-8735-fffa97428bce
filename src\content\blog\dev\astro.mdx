---
title: Astro 静态站点开发指南
description: 通过实例讲解 Astro 框架的核心概念和开发流程，包括项目搭建、路由配置、组件开发、构建优化以及生产部署全过程
date: "2024-09-25"
category: "前端开发"
tags:
- Astro
- 静态站点
- 性能优化
- 组件开发
- 路由系统
- Islands架构
---

## 网站部署

### 前置条件

- 安装 [Node.js](https://nodejs.org/)

> 推荐 `v18.17.1` 或 `v20.3.0` 及以上版本。`v19` 不支持。

- 下载 [VS Code](https://code.visualstudio.com/#alt-downloads) 或选择其他代码编辑器。

### 创建项目

#### 使用模板创建

我们可以直接下载别人的项目文件，安装好依赖，即可在本地运行。

> 以 Fuwari 主题为例。

1. 使用此模板[生成新仓库](https://github.com/saicaca/fuwari/generate)或 Fork 此仓库。

2. C<PERSON> 新的仓库，依次执行以下命令，安装依赖：

```sh
pnpm install
```

```sh
pnpm add sharp
```

若未安装 [pnpm](https://pnpm.io/)，则执行：

```sh
npm install -g pnpm
```

执行结束，项目便创建好了。

#### 创建空白项目

如果想完全从 0 开始创建，可以运行以下命令，启动 Astro 设置向导：

```sh
npm create astro@latest
```

完成后，继续运行以下命令，执行安装：

```sh
create-astro
```

当提示 `Where would you like to create your new project?`（你想要在哪里创建你的新项目？）时，输入一个文件夹名称，为你的项目创建一个新目录。例如：`./tutorial`。

> 注意：新的 Astro 项目只能在一个完全空的文件夹中创建，因此请为你的文件夹选择一个尚不存在的名称！  
> 你将看到一个简短的入门模板列表可供选择。使用方向键（上和下）导航至模板，然后按回车键（回车）选择对应模板。

当提示询问你是否打算编写 TypeScript 时，输入 `n`（不打算）。

当提示询问 `Would you like to install dependencies?`（你想现在安装依赖吗？）时输入 `y`（现在安装）。

当提示询问 `Would you like to initialize a new git repository?`（你想要初始化一个新的 Git 仓库吗？）时输入 `y`（要初始化）。

### 安装扩展

用 VS Code 打开 Astro 项目文件夹，应该会看到一条通知，询问是否要安装推荐的扩展。

点击查看推荐扩展，选择 [Astro 语言支持](https://marketplace.visualstudio.com/items?itemName=astro-build.astro-vscode)。这将为你的 Astro 代码提供语法提示和自动代码补全功能。

如果没有通知，也可通过以上链接自行安装。

### 本地运行

为了实时预览项目，需要在开发 (dev) 模式下运行 Astro。

输入以下命令，启动 Astro 开发服务：

```sh
npm run dev
```

用浏览器打开终端窗口中的 `localhost` 链接，进行实时预览。

> （如果端口 4321 可用，Astro 默认使用 `http://localhost:4321`）。  
> 当 Astro 服务器以开发模式运行时，无法在终端窗口中运行命令。可以随时在终端中键入 Ctrl + C 返回到命令提示符。

输入以下命令，可生成静态网站文件至 `./dist/` 目录：

```sh
pnpm build
```

输入以下命令，即可在 localhost:4321 启动本地开发服务器，预览待上传的网站：

```sh
pnpm preview
```

### 代码托管

参考[官方指南](https://docs.astro.build/zh-cn/guides/deploy/)将网站部署至 [Vercel](https://vercel.com/)、[Netlify](https://netlify.com/) 、GitHub Pages 等平台；部署前需编辑 `astro.config.mjs` 中的站点设置。

打开代码编辑器，导航至 `src/pages/index.astro`，修改内容。

> 通过配置文件 `src/config.ts` 自定义博客
> 将 `<html lang="en">` 改为 `<html lang="zh-cn">`
> 将 `<head>` 中的 `<title>` 改为自己的网站标题
> 在 `<body>` 中增加内容

## Markdown 语法

### 页眉选项（Front-matter）

```yaml
---
title: My First Blog Post
published: 2023-09-09
description: This is the first post of my new Astro blog.
image: ./cover.jpg
tags: [Foo, Bar]
category: Front-end
draft: false
---
```

### GitHub 卡片

输入以下代码即可：

```markdown
::github{repo="<用户名>/<仓库名>"}
```

## 常用命令

创建新文章：

```sh
pnpm new-post <文件名>
```

显示 Astro CLI 帮助：

```sh
pnpm astro --help
```

## 参考资料

> [Astro 官方文档](https://docs.astro.build/zh-cn/community-resources/content/)  
> [Astro 教程源码](https://github.com/withastro/blog-tutorial-demo/tree/complete) 、 [StackBlitz](https://stackblitz.com/github/withastro/blog-tutorial-demo/tree/complete?file=src/pages/index.astro)  
> [使用内容集合](https://docs.astro.build/zh-cn/tutorials/add-content-collections/)  
> [使用视图过渡动画](https://docs.astro.build/zh-cn/tutorials/add-view-transitions/)