/**
 * 统一的 Zod Schema 定义
 * 提供运行时类型验证和 TypeScript 类型推导
 */

import { z } from 'zod';

// ==================== 基础 Schema ====================

/** URL Schema */
export const urlSchema = z.string().url();

/** 时间戳 Schema */
export const timestampSchema = z.string().datetime();

/** 颜色 Schema */
export const colorSchema = z.string().regex(/^#[0-9A-Fa-f]{6}$/);

/** ID Schema */
export const idSchema = z.string().min(1);

/** Slug Schema */
export const slugSchema = z.string().regex(/^[a-z0-9-]+$/);

// ==================== 内容基础 Schema ====================

/** 基础 Frontmatter Schema */
export const baseFrontmatterSchema = z.object({
  title: z.string().min(1),
  description: z.string().optional(),
  date: timestampSchema,
  published: z.boolean().default(true),
  tags: z.array(z.string()).default([]),
  author: z.string().optional(),
  slug: slugSchema.optional(),
});

/** 分类 Schema */
export const categorySchema = z.object({
  id: idSchema,
  name: z.string().min(1),
  description: z.string().optional(),
  slug: slugSchema,
  count: z.number().int().min(0).default(0),
});

// ==================== 博客相关 Schema ====================

/** 博客文章 Frontmatter Schema */
export const blogFrontmatterSchema = baseFrontmatterSchema.extend({
  category: z.string().optional(),
  excerpt: z.string().optional(),
  coverImage: urlSchema.optional(),
  readingTime: z.number().int().min(0).optional(),
  featured: z.boolean().default(false),
  series: z.string().optional(),
  seriesOrder: z.number().int().min(1).optional(),
});

/** 博客文章 Schema */
export const blogPostSchema = z.object({
  slug: slugSchema,
  frontmatter: blogFrontmatterSchema,
  content: z.string(),
  excerpt: z.string().optional(),
  readingTime: z.number().int().min(0).default(0),
  wordCount: z.number().int().min(0).default(0),
});

/** 博客分类 Schema */
export const blogCategorySchema = categorySchema;

/** 标签统计 Schema */
export const tagCountSchema = z.object({
  name: z.string().min(1),
  count: z.number().int().min(0),
});

// ==================== 文档相关 Schema ====================

/** 文档 Frontmatter Schema */
export const docFrontmatterSchema = baseFrontmatterSchema.extend({
  category: z.string(),
  order: z.number().int().min(0).optional(),
  toc: z.boolean().default(true),
  lastModified: timestampSchema.optional(),
  contributors: z.array(z.string()).default([]),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
});

/** 文档项 Schema */
export const docItemSchema = z.object({
  slug: slugSchema,
  frontmatter: docFrontmatterSchema,
  content: z.string(),
  headings: z
    .array(
      z.object({
        id: idSchema,
        text: z.string(),
        level: z.number().int().min(1).max(6),
      })
    )
    .default([]),
});

/** 文档分类 Schema */
export const docCategorySchema = categorySchema.extend({
  order: z.number().int().min(0).default(0),
  icon: z.string().optional(),
});

// ==================== 工具相关 Schema ====================

/** 工具状态 Schema */
export const toolStatusSchema = z.enum(['stable', 'beta', 'experimental']);

/** 工具难度 Schema */
export const toolDifficultySchema = z.enum(['easy', 'medium', 'hard']);

/** 工具 Schema */
export const toolSchema = z.object({
  id: idSchema,
  name: z.string().min(1),
  description: z.string(),
  category: z.string(),
  tags: z.array(z.string()).default([]),
  url: z.string(),
  icon: z.string().optional(),
  status: toolStatusSchema.default('stable'),
  difficulty: toolDifficultySchema.optional(),
  featured: z.boolean().default(false),
  external: z.boolean().default(false),
});

// ==================== 链接相关 Schema ====================

/** 图标类型 Schema */
export const iconTypeSchema = z.enum(['emoji', 'lucide', 'custom']);

/** 链接项 Schema */
export const linksItemSchema = z.object({
  id: idSchema,
  title: z.string().min(1),
  description: z.string(),
  url: urlSchema,
  category: z.string(),
  tags: z.array(z.string()).default([]),
  icon: z.string().optional(),
  iconType: iconTypeSchema.default('emoji'),
  featured: z.boolean().default(false),
  order: z.number().int().min(0).default(0),
});

/** 链接分类 Schema */
export const linksCategorySchema = categorySchema.extend({
  icon: z.string().optional(),
  color: colorSchema.optional(),
});

// ==================== 搜索相关 Schema ====================

/** 搜索结果 Schema */
export const searchResultSchema = z.object({
  id: idSchema,
  title: z.string(),
  description: z.string().optional(),
  url: z.string(),
  type: z.enum(['blog', 'doc', 'tool', 'link', 'command']),
  category: z.string().optional(),
  tags: z.array(z.string()).default([]),
  score: z.number().min(0).max(1).optional(),
  highlight: z.string().optional(),
});

// ==================== API 相关 Schema ====================

/** API 响应 Schema */
export const apiResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    success: z.boolean(),
    data: dataSchema.optional(),
    error: z.string().optional(),
    message: z.string().optional(),
    timestamp: timestampSchema.optional(),
  });

/** 分页信息 Schema */
export const paginationSchema = z.object({
  page: z.number().int().min(1),
  limit: z.number().int().min(1).max(100),
  total: z.number().int().min(0),
  totalPages: z.number().int().min(0),
  hasNext: z.boolean(),
  hasPrev: z.boolean(),
});

/** 分页数据 Schema */
export const paginatedDataSchema = <T extends z.ZodTypeAny>(itemSchema: T) =>
  z.object({
    items: z.array(itemSchema),
    pagination: paginationSchema,
  });

// ==================== 配置相关 Schema ====================

/** 主题配置 Schema */
export const themeConfigSchema = z.object({
  mode: z.enum(['light', 'dark', 'system']).default('system'),
  enableTransitions: z.boolean().default(true),
  enableAnimations: z.boolean().default(true),
});

/** 缓存配置 Schema */
export const cacheConfigSchema = z.object({
  ttl: z.number().int().min(0),
  maxSize: z.number().int().min(1),
  enabled: z.boolean().default(true),
});

// ==================== 类型推导 ====================

/** 从 Schema 推导的 TypeScript 类型 */
export type BlogPost = z.infer<typeof blogPostSchema>;
export type BlogFrontmatter = z.infer<typeof blogFrontmatterSchema>;
export type BlogCategory = z.infer<typeof blogCategorySchema>;
export type TagCount = z.infer<typeof tagCountSchema>;

export type DocItem = z.infer<typeof docItemSchema>;
export type DocFrontmatter = z.infer<typeof docFrontmatterSchema>;
export type DocCategory = z.infer<typeof docCategorySchema>;

export type Tool = z.infer<typeof toolSchema>;
export type ToolStatus = z.infer<typeof toolStatusSchema>;
export type ToolDifficulty = z.infer<typeof toolDifficultySchema>;

export type LinksItem = z.infer<typeof linksItemSchema>;
export type LinksCategory = z.infer<typeof linksCategorySchema>;
export type IconType = z.infer<typeof iconTypeSchema>;

export type SearchResult = z.infer<typeof searchResultSchema>;
export type PaginationInfo = z.infer<typeof paginationSchema>;

export type ThemeConfig = z.infer<typeof themeConfigSchema>;
export type CacheConfig = z.infer<typeof cacheConfigSchema>;

// ==================== 验证工具函数 ====================

/** 安全解析函数 */
export const safeParse = <T extends z.ZodTypeAny>(
  schema: T,
  data: unknown
): { success: true; data: z.infer<T> } | { success: false; error: z.ZodError } => {
  const result = schema.safeParse(data);
  return result;
};

/** 验证并抛出错误 */
export const validateOrThrow = <T extends z.ZodTypeAny>(
  schema: T,
  data: unknown,
  errorMessage?: string
): z.infer<T> => {
  try {
    return schema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new Error(errorMessage || `Validation failed: ${error.message}`);
    }
    throw error;
  }
};

/** 部分验证（允许部分字段） */
export const validatePartial = <T extends z.ZodTypeAny>(
  schema: T,
  data: unknown
): z.infer<z.ZodPartial<T>> => {
  return schema.partial().parse(data);
};
