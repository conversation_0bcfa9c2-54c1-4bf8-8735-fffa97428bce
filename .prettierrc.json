{"semi": true, "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "printWidth": 100, "useTabs": false, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "quoteProps": "as-needed", "jsxSingleQuote": false, "jsxBracketSameLine": false, "proseWrap": "preserve", "overrides": [{"files": "*.{ts,tsx}", "options": {"parser": "typescript"}}, {"files": "*.{js,jsx,mjs}", "options": {"parser": "babel"}}, {"files": "*.{css,scss}", "options": {"parser": "css"}}, {"files": "*.{json,jsonc}", "options": {"parser": "json"}}, {"files": "*.md", "options": {"parser": "markdown"}}]}