/**
 * 统一样式导出
 * 提供项目中所有样式相关的统一入口
 */

// ==================== 全局样式 ====================
import './globals.css';

// ==================== 组件样式变量 ====================
export const COMPONENT_STYLES = {
  // 卡片样式
  card: {
    base: 'rounded-lg border bg-card text-card-foreground shadow-sm',
    hover: 'hover:shadow-md transition-shadow duration-200',
    interactive: 'cursor-pointer hover:scale-[1.02] transition-transform duration-200',
  },

  // 按钮样式
  button: {
    base: 'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background',
    primary: 'bg-primary text-primary-foreground hover:bg-primary/90',
    secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
    outline: 'border border-input hover:bg-accent hover:text-accent-foreground',
    ghost: 'hover:bg-accent hover:text-accent-foreground',
  },

  // 输入框样式
  input: {
    base: 'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
    error: 'border-destructive focus-visible:ring-destructive',
  },

  // 导航样式
  nav: {
    base: 'flex items-center space-x-4 lg:space-x-6',
    item: 'text-sm font-medium transition-colors hover:text-primary',
    active: 'text-primary',
    inactive: 'text-muted-foreground',
  },

  // 标签样式
  badge: {
    base: 'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
    default: 'border-transparent bg-primary text-primary-foreground hover:bg-primary/80',
    secondary: 'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80',
    outline: 'text-foreground',
  },

  // 代码样式
  code: {
    inline: 'relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold',
    block: 'relative rounded-md bg-muted p-4 font-mono text-sm',
  },

  // 分隔符样式
  separator: {
    horizontal: 'shrink-0 bg-border h-[1px] w-full',
    vertical: 'shrink-0 bg-border w-[1px] h-full',
  },

  // 骨架屏样式
  skeleton: {
    base: 'animate-pulse rounded-md bg-muted',
    text: 'h-4 bg-muted rounded',
    avatar: 'h-12 w-12 rounded-full bg-muted',
    button: 'h-10 w-20 bg-muted rounded-md',
  },
} as const;

// ==================== 动画样式 ====================
export const ANIMATIONS = {
  // 淡入淡出
  fadeIn: 'animate-in fade-in-0 duration-200',
  fadeOut: 'animate-out fade-out-0 duration-200',

  // 滑动
  slideInFromTop: 'animate-in slide-in-from-top-2 duration-200',
  slideInFromBottom: 'animate-in slide-in-from-bottom-2 duration-200',
  slideInFromLeft: 'animate-in slide-in-from-left-2 duration-200',
  slideInFromRight: 'animate-in slide-in-from-right-2 duration-200',

  // 缩放
  scaleIn: 'animate-in zoom-in-95 duration-200',
  scaleOut: 'animate-out zoom-out-95 duration-200',

  // 组合动画
  dialogIn: 'animate-in fade-in-0 zoom-in-95 duration-200',
  dialogOut: 'animate-out fade-out-0 zoom-out-95 duration-200',

  dropdownIn: 'animate-in fade-in-0 slide-in-from-top-2 duration-200',
  dropdownOut: 'animate-out fade-out-0 slide-out-to-top-2 duration-200',
} as const;

// ==================== 响应式断点 ====================
export const BREAKPOINTS = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
} as const;

// ==================== 间距系统 ====================
export const SPACING = {
  xs: '0.25rem', // 4px
  sm: '0.5rem', // 8px
  md: '1rem', // 16px
  lg: '1.5rem', // 24px
  xl: '2rem', // 32px
  '2xl': '3rem', // 48px
  '3xl': '4rem', // 64px
} as const;

// ==================== 颜色系统 ====================
export const COLORS = {
  // 主色调
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    500: '#3b82f6',
    600: '#2563eb',
    900: '#1e3a8a',
  },

  // 灰色调
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
  },

  // 状态颜色
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
  info: '#3b82f6',
} as const;

// ==================== Z-Index 层级 ====================
export const Z_INDEX = {
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modalBackdrop: 1040,
  modal: 1050,
  popover: 1060,
  tooltip: 1070,
  toast: 1080,
} as const;

// ==================== 阴影系统 ====================
export const SHADOWS = {
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
} as const;

// ==================== 字体系统 ====================
export const FONTS = {
  sans: ['Inter', 'system-ui', 'sans-serif'],
  mono: ['JetBrains Mono', 'Consolas', 'Monaco', 'monospace'],
  serif: ['Georgia', 'Times New Roman', 'serif'],
} as const;
