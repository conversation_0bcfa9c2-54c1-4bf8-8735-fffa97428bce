/**
 * MDX 组件配置
 * 统一管理所有 MDX 组件的配置和映射
 */

import * as React from 'react';
import type { MDXComponents } from '@/types';

// Base components - 使用 kebab-case 文件名
import { MDXImage } from '@/components/mdx/mdx-image';
import { MDXLink } from '@/components/mdx/mdx-link';
import { MDXCodeBlock } from '@/components/mdx/mdx-code-block';
import { MDXBlockquote } from '@/components/mdx/mdx-blockquote';
import { MDXCodeInline } from '@/components/mdx/mdx-code-inline';
import { MDXTableComponents } from '@/components/mdx/mdx-table';

// Code style components
import {
  PrimaryCode,
  SecondaryCode,
  SuccessCode,
  WarningCode,
  ErrorCode,
} from '@/components/mdx/mdx-code-inline';

// Extended components
import { MDXTabs } from '@/components/mdx/MDXTabs';
import { MDXAccordion } from '@/components/mdx/MDXAccordion';
import { MDXVideo } from '@/components/mdx/MDXVideo';
import { MDXImageZoom } from '@/components/mdx/MDXImageZoom';
import { MDXCodeDemo } from '@/components/mdx/MDXCodeDemo';
import { MDXCodeGroup } from '@/components/mdx/MDXCodeGroup';

// Heading components
import { H1, H2, H3, H4, H5, H6 } from '@/components/mdx/mdx-heading';

/**
 * MDX 组件映射配置
 */
export const MDXComponentsMapping = {
  // 基础 HTML 组件映射
  img: MDXImage,
  a: MDXLink,
  blockquote: MDXBlockquote,

  // 标题组件映射
  h1: H1,
  h2: H2,
  h3: H3,
  h4: H4,
  h5: H5,
  h6: H6,

  // 智能代码组件映射
  code: MDXCodeInline,
  pre: MDXCodeBlock,
  table: MDXTableComponents.table,
  thead: MDXTableComponents.thead,
  tbody: MDXTableComponents.tbody,
  tr: MDXTableComponents.tr,
  th: MDXTableComponents.th,
  td: MDXTableComponents.td,

  // 代码样式变体
  'code.primary': PrimaryCode,
  'code.secondary': SecondaryCode,
  'code.success': SuccessCode,
  'code.warning': WarningCode,
  'code.error': ErrorCode,

  // 扩展组件
  Tabs: MDXTabs,
  Accordion: MDXAccordion,
  Video: MDXVideo,
  ImageZoom: MDXImageZoom,
  CodeDemo: MDXCodeDemo,
  CodeGroup: MDXCodeGroup,

  // 样式化代码组件的直接映射
  PrimaryCode,
  SecondaryCode,
  SuccessCode,
  WarningCode,
  ErrorCode,
};

/**
 * 默认的组件 Props
 */
export const defaultComponentProps = {
  img: {
    loading: 'lazy' as const,
    decoding: 'async' as const,
  },
  a: {
    target: '_blank' as const,
    rel: 'noopener noreferrer' as const,
  },
  pre: {
    showLineNumbers: true,
  },
  code: {
    inline: true,
  },
  table: {
    striped: true,
    hover: true,
    compact: false,
  },
};

/**
 * 组件上下文类型
 */
export interface MDXComponentContextType {
  components: MDXComponents;
  defaultProps: typeof defaultComponentProps;
}

/**
 * 创建组件上下文
 */
export const MDXComponentContext = React.createContext<MDXComponentContextType>({
  components: MDXComponentsMapping,
  defaultProps: defaultComponentProps,
});

/**
 * 组件上下文 Hook
 */
export const useMDXComponents = (): MDXComponentContextType =>
  React.useContext(MDXComponentContext);
