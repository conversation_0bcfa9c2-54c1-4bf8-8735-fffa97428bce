'use client';

import React, { useMemo } from 'react';
import Link from 'next/link';
import { ExternalLinkIcon } from 'lucide-react';
import { cn } from '@/utils';
import { MDXBaseOptions } from '@/config/mdx';

/**
 * MDX链接组件的属性定义
 */
export interface MDXLinkProps extends React.AnchorHTMLAttributes<HTMLAnchorElement> {
  /** 链接地址 */
  href: string;
  /** 链接内容 */
  children: React.ReactNode;
  /** 自定义类名 */
  className?: string;
  /** 是否强制作为外部链接处理 */
  external?: boolean;
  /** 是否在新标签页打开 */
  openInNewTab?: boolean;
  /** 是否显示外部链接图标 */
  showExternalIcon?: boolean;
}

// 样式常量
const linkStyles = {
  base: [
    'relative inline-flex items-center gap-1',
    'text-foreground no-underline',
    'transition-all duration-200',
    'hover:text-primary',
    'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
  ].join(' '),
  external: 'cursor-alias',
  icon: 'h-3 w-3 flex-shrink-0',
  underline: [
    'after:absolute after:left-1/2 after:bottom-0',
    'after:h-px after:w-0 after:bg-current',
    'after:transition-all after:duration-300',
    'hover:after:left-0 hover:after:w-full',
  ].join(' '),
} as const;

// 工具函数
const isExternalLink = (href: string): boolean => {
  try {
    const url = new URL(href, window.location.origin);
    return url.origin !== window.location.origin;
  } catch {
    return href.startsWith('mailto:') || href.startsWith('tel:');
  }
};

/**
 * MDX 链接组件
 *
 * 功能特性：
 * - 自动识别内部/外部链接
 * - 外部链接图标显示
 * - 新标签页打开配置
 * - 下划线动画效果
 * - 可访问性支持
 * - 性能优化
 */
export const MDXLink = React.forwardRef<HTMLAnchorElement, MDXLinkProps>(
  ({ href, children, className, external, openInNewTab, showExternalIcon, ...props }, ref) => {
    // 性能优化：缓存计算结果
    const linkConfig = useMemo(() => {
      const isExternal = external ?? isExternalLink(href);
      const shouldShowIcon = showExternalIcon ?? MDXBaseOptions.link.externalIcon;
      const shouldShowUnderline = MDXBaseOptions.link.underline;
      const shouldOpenInNewTab = openInNewTab ?? MDXBaseOptions.link.openExternalInNewTab;

      return {
        isExternal,
        shouldShowIcon: isExternal && shouldShowIcon,
        shouldShowUnderline,
        shouldOpenInNewTab: isExternal && shouldOpenInNewTab,
      };
    }, [href, external, openInNewTab, showExternalIcon]);

    // 组合样式类名
    const linkClasses = cn(
      linkStyles.base,
      linkConfig.isExternal && linkStyles.external,
      linkConfig.shouldShowUnderline && linkStyles.underline,
      className
    );

    // 渲染外部链接
    if (linkConfig.isExternal) {
      return (
        <a
          ref={ref}
          href={href}
          className={linkClasses}
          target={linkConfig.shouldOpenInNewTab ? '_blank' : undefined}
          rel="noopener noreferrer"
          {...props}
        >
          {children}
          {linkConfig.shouldShowIcon && (
            <ExternalLinkIcon className={linkStyles.icon} aria-hidden="true" />
          )}
        </a>
      );
    }

    // 渲染内部链接
    return (
      <Link ref={ref} href={href} className={linkClasses} {...props}>
        {children}
      </Link>
    );
  }
);

MDXLink.displayName = 'MDXLink';
