/**
 * Zustand 状态管理配置
 * 统一管理状态存储的配置和工具函数
 */

import { persist, createJSONStorage } from 'zustand/middleware';
import type { PersistConfig, StateSliceConfig } from './types';

// ==================== 存储配置 ====================

/**
 * 默认的本地存储配置
 */
export const DEFAULT_STORAGE_CONFIG = {
  storage: createJSONStorage(() => localStorage),
  partialize: (state: any) => state,
  onRehydrateStorage: () => (state: any) => {
    console.log('State rehydrated:', state);
  },
} as const;

/**
 * 会话存储配置
 */
export const SESSION_STORAGE_CONFIG = {
  storage: createJSONStorage(() => sessionStorage),
  partialize: (state: any) => state,
} as const;

// ==================== 持久化配置 ====================

/**
 * 主题状态持久化配置
 */
export const THEME_PERSIST_CONFIG: PersistConfig = {
  name: 'iflux-theme-storage',
  ...DEFAULT_STORAGE_CONFIG,
  partialize: (state: any) => ({
    theme: state.theme,
  }),
};

/**
 * 认证状态持久化配置
 */
export const AUTH_PERSIST_CONFIG: PersistConfig = {
  name: 'iflux-auth-storage',
  ...DEFAULT_STORAGE_CONFIG,
  partialize: (state: any) => ({
    isLoggedIn: state.isLoggedIn,
    loginTime: state.loginTime,
  }),
};

/**
 * 搜索历史持久化配置
 */
export const SEARCH_PERSIST_CONFIG: PersistConfig = {
  name: 'iflux-search-storage',
  ...DEFAULT_STORAGE_CONFIG,
  partialize: (state: any) => ({
    history: state.history,
  }),
};

/**
 * 工具状态持久化配置
 */
export const TOOL_PERSIST_CONFIG: PersistConfig = {
  name: 'iflux-tool-storage',
  ...DEFAULT_STORAGE_CONFIG,
  partialize: (state: any) => ({
    history: state.history,
  }),
};

/**
 * 缓存状态持久化配置（使用会话存储）
 */
export const CACHE_PERSIST_CONFIG: PersistConfig = {
  name: 'iflux-cache-storage',
  ...SESSION_STORAGE_CONFIG,
  partialize: (state: any) => ({
    cache: Array.from(state.cache.entries()),
  }),
};

// ==================== 状态切片配置 ====================

/**
 * 主题状态切片配置
 */
export const THEME_SLICE_CONFIG: StateSliceConfig<any> = {
  name: 'theme',
  initialState: {
    theme: 'system' as const,
  },
  persist: true,
  persistConfig: THEME_PERSIST_CONFIG,
};

/**
 * 认证状态切片配置
 */
export const AUTH_SLICE_CONFIG: StateSliceConfig<any> = {
  name: 'auth',
  initialState: {
    isLoggedIn: false,
    loginTime: null,
  },
  persist: true,
  persistConfig: AUTH_PERSIST_CONFIG,
};

/**
 * 搜索状态切片配置
 */
export const SEARCH_SLICE_CONFIG: StateSliceConfig<any> = {
  name: 'search',
  initialState: {
    isOpen: false,
    query: '',
    results: [],
    isLoading: false,
    history: [],
    selectedIndex: 0,
  },
  persist: true,
  persistConfig: SEARCH_PERSIST_CONFIG,
};

/**
 * 导航栏状态切片配置
 */
export const NAVBAR_SLICE_CONFIG: StateSliceConfig<any> = {
  name: 'navbar',
  initialState: {
    direction: 'up' as const,
    position: 0,
    showTitle: false,
    pageTitle: '',
    lastDirectionChange: 0,
  },
  persist: false,
};

/**
 * 工具状态切片配置
 */
export const TOOL_SLICE_CONFIG: StateSliceConfig<any> = {
  name: 'tool',
  initialState: {
    input: '',
    output: '',
    loading: false,
    error: null,
    history: [],
  },
  persist: true,
  persistConfig: TOOL_PERSIST_CONFIG,
};

/**
 * 过滤状态切片配置
 */
export const FILTER_SLICE_CONFIG: StateSliceConfig<any> = {
  name: 'filter',
  initialState: {
    selectedCategory: '',
    selectedTag: null,
    searchTerm: '',
  },
  persist: false,
};

/**
 * 缓存状态切片配置
 */
export const CACHE_SLICE_CONFIG: StateSliceConfig<any> = {
  name: 'cache',
  initialState: {
    cache: new Map(),
  },
  persist: true,
  persistConfig: CACHE_PERSIST_CONFIG,
};

// ==================== 工具函数 ====================

/**
 * 创建持久化中间件
 */
export function createPersistMiddleware<T>(config: PersistConfig) {
  return persist<T>((set, get, api) => ({}) as T, config);
}

/**
 * 创建状态切片
 */
export function createStateSlice<T>(config: StateSliceConfig<T>) {
  const { name, initialState, persist: shouldPersist, persistConfig } = config;

  if (shouldPersist && persistConfig) {
    return createPersistMiddleware<T>(persistConfig);
  }

  return (set: any, get: any, api: any) => initialState;
}

/**
 * 状态重置工具
 */
export function createResetFunction<T>(initialState: T) {
  return (set: (state: T | Partial<T>) => void) => {
    set(initialState);
  };
}

/**
 * 状态验证工具
 */
export function validateState<T>(state: T, schema: any): boolean {
  try {
    // 这里可以集成 Zod 或其他验证库
    return true;
  } catch (error) {
    console.error('State validation failed:', error);
    return false;
  }
}

// ==================== 常量配置 ====================

/**
 * 状态管理常量
 */
export const STATE_CONFIG = {
  // 缓存过期时间（毫秒）
  CACHE_TTL: 5 * 60 * 1000, // 5分钟

  // 登录过期时间（毫秒）
  LOGIN_TIMEOUT: 24 * 60 * 60 * 1000, // 24小时

  // 搜索历史最大条数
  MAX_SEARCH_HISTORY: 10,

  // 工具历史最大条数
  MAX_TOOL_HISTORY: 20,

  // 滚动阈值
  SCROLL_THRESHOLD: 3,
  SHOW_THRESHOLD: 80,
  HIDE_THRESHOLD: 120,

  // 防抖延迟
  DEBOUNCE_DELAY: 300,

  // 节流延迟
  THROTTLE_DELAY: 50,
} as const;

/**
 * 存储键名
 */
export const STORAGE_KEYS = {
  THEME: 'iflux-theme-storage',
  AUTH: 'iflux-auth-storage',
  SEARCH: 'iflux-search-storage',
  TOOL: 'iflux-tool-storage',
  CACHE: 'iflux-cache-storage',
} as const;

export default {
  DEFAULT_STORAGE_CONFIG,
  SESSION_STORAGE_CONFIG,
  STATE_CONFIG,
  STORAGE_KEYS,
  createPersistMiddleware,
  createStateSlice,
  createResetFunction,
  validateState,
};
