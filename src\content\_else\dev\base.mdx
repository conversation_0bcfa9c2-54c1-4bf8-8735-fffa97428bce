---
title: Web 基础
date: 2025-05-01
category: 开发
tags:
  - HTML
  - CSS
  - JavaScript
  - 前端
  - 教程
description: Web 开发基础知识指南，涵盖 HTML、CSS 和 JavaScript 的核心概念，包括文件路由规则、字符引用和 CSS 继承等基础知识
---

HTML、CSS、JavaScript 是构建网页的基础技术。

- HTML（超文本标记语言）用于定义网页的结构和内容。
- CSS（层叠样式表）用于定义网页的样式和布局。
- JavaScript 和 API 用于添加交互和动态效果。

## HTML

### 通用文件路由规则

当引用的目标文件与 HTML 文件处于同一目录层级时，你只需直接使用文件名即可完成引用。

> 例如，若要引用与 HTML 文件同级的图片文件，可直接写为 `my-image.jpg`。

若你需要引用子目录中的文件，应当在路径前面写上对应的目录名，然后加上一个正斜杠。

> 举例来说，若要引用 `subdirectory` 子目录下的 `my-image.jpg` 文件，引用路径可写为 `subdirectory/my-image.jpg`。

倘若引用的目标文件位于 HTML 文件所在目录的上级目录，此时需要在路径前加上两个点（`..`）。

> 例如，假设 `index.html` 文件位于 `test-site` 目录的一个子文件夹内，而 `my-image.jpg` 文件就在 `test-site` 目录下，那么从 `index.html` 文件引用 `my-image.jpg` 文件时，可使用 `../my-image.jpg` 这样的路径。

### 字符引用

HTML 中，有些字符需要用特殊的字符引用表示，否则会被解析为 HTML 标签。

常见字符引用如下：

| 符号 | 字符引用 | 名称 |
| ---- | ---- | ---- |
| &lt; | `&lt;` | 小于号 |
| &gt; | `&gt;` | 大于号 |
| &amp; | `&amp;` | 和号 |
| &quot; | `&quot;` | 双引号 |
| &apos; | `&apos;` | 单引号 |

### 控制继承

CSS 提供了五个特殊的通用属性值来控制继承行为，这些值可以应用于任何 CSS 属性：

- `inherit`：强制子元素继承父元素的属性值，即显式开启继承机制。

- `initial`：将属性值重置为该属性的规范定义初始值。

- `revert`：将属性值重置为浏览器的默认样式，而不是该属性的规范默认值。在大多数情况下，其行为与 `unset` 类似。

- `revert-layer`：将属性值重置为上一个层叠层（cascade layer）中定义的值。

- `unset`：根据属性的自然继承特性决定行为：
  - 如果属性默认是可继承的，则表现为 `inherit`
  - 如果属性默认是不可继承的，则表现为 `initial`

这些属性值可以应用于任何 CSS 属性，包括：
- 文本属性：`color`、`font`、`text-align` 等
- 盒模型属性：`width`、`height`、`margin`、`padding` 等
- 背景属性：`background`、`background-color`、`background-image` 等
- 边框属性：`border`、`border-color`、`border-width` 等
- 布局属性：`display`、`position`、`float` 等
- 其他属性：`opacity`、`transform`、`transition` 等

## 服务器端语言和框架

HTML、CSS 和 JavaScript 是前端（客户端）语言，在浏览器运行，构建用户交互的前端界面。

后端（服务器端）语言在服务器执行操作，处理完将结果发至浏览器。常见操作是从数据库取数据，生成含数据的 HTML 再发送给用户。

典型服务器端框架和语言有：ASP.NET（C#）、Django（Python）、Laravel（PHP）、Next.js（JavaScript）。

客户端和服务器端语言也可分为静态和动态。纯 HTML 存于服务器，请求时原封不动传至客户端渲染，称“静态”文件。而服务器端代码生成的 HTML 内容会随代码逻辑变化，称“动态”文件。

不过，静态和动态概念并非绝对，有重叠。服务器端语言常通过模板文件定义 HTML 结构，多为静态，含根据数据变化的动态部分。

## 命令行基础

### 基本路径操作

```powershell
# 切换目录
cd C:\Program Files
```

```powershell
# 返回上级目录
cd ..
```

```powershell
# 显示当前路径
chdir
```

### 目录操作

```powershell
# 创建新目录
mkdir project
```

```powershell
# 创建多级目录（自动创建不存在的父目录）
mkdir parent\child\grandchild
```

```powershell
# 删除空目录（需确保目录为空）
rmdir empty_folder
```

### 文件操作

```powershell
# 列出目录内容（显示隐藏文件）
dir /A
```

```powershell
# 复制文件（保留元数据）
copy source.txt destination\backup.txt
```

```powershell
# 移动/重命名文件
move oldname.txt renamed.txt
```

### 网络诊断

```powershell
# 持续测试网络连通性（默认4次）
ping baidu.com
```

```powershell
# 显示完整网络配置（包含MAC地址）
ipconfig /all
```

```powershell
# 强制刷新DNS解析缓存
ipconfig /flushdns
```

### 进程管理

```powershell
# 列出所有运行进程（包含系统服务）
tasklist
```

```powershell
# 强制结束指定进程（/F参数）
taskkill /IM notepad.exe /F
```

### 命令历史

```powershell
# 查看命令历史
doskey /history
```

```powershell
# 清空屏幕
cls
```