---
title: Next.js 开发指南
description: Next.js 是一个基于 React 的全栈 Web 应用框架，本文详细介绍其核心功能、开发流程、社区生态以及最佳实践
date: "2025-04-09"
category: "前端开发"
tags:
- Next.js
- React
- 全栈开发
- 服务端渲染
- 静态生成
- 路由系统
---

## 简介

Next.js 是一个被全球众多知名企业广泛采用的框架，旨在通过扩展最新的 React 特性和集成高效的基于 Rust 的 JavaScript 工具，帮助开发者快速构建全栈 Web 应用程序。

- 开始学习 Next.js，可以访问 [学习 Next.js](https://nextjs.org/learn) 课程。
- 想要查看更多使用 Next.js 构建的网站，欢迎访问 [Next.js 展示](https://nextjs.org/showcase)。

## 文档

完整的 Next.js 文档可以在 [Next.js 文档](https://nextjs.org/docs) 中找到。

## 社区

Next.js 的社区活跃于 [GitHub 讨论区](https://github.com/vercel/next.js/discussions)，在这里用户可以提问、分享想法以及展示自己的项目。

此外，用户还可以加入 Next.js 的 [Discord](https://nextjs.org/discord) 服务器，与其他社区成员进行实时交流。

请注意，[行为准则](https://github.com/vercel/next.js/blob/canary/CODE_OF_CONDUCT.md) 适用于所有 Next.js 社区频道，**强烈建议**大家阅读并遵守。

## 贡献

对 Next.js 的贡献始终受到欢迎和重视。在参与之前，建议查看 [贡献指南](/contributing.md)，以确保贡献过程顺利。

社区中有一个 **[好的初学者问题](https://github.com/vercel/next.js/labels/good%20first%20issue)** 列表，包含了一些相对简单的错误，适合新手和初学者入门，积累经验并熟悉贡献流程。

## 安全

如果您发现了 Next.js 的安全漏洞，建议**负责任地披露此信息，而不是公开问题**。Next.js 团队会认真对待所有合法的报告。

请利用 GitHub 的私密漏洞报告功能，向我们报告潜在的安全漏洞。访问 [https://github.com/vercel/next.js/security](https://github.com/vercel/next.js/security) 并点击“报告漏洞”按钮。

## 项目初始化

### 安装 Node.js

[Node.js](https://nodejs.org/) 是一个基于 Chrome V8 引擎的 JavaScript 运行环境。

### 安装依赖

```bash
pnpm install next@latest react@latest react-dom@latest
```