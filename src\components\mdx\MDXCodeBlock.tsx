'use client';

import React, { type HTMLAttributes, useState, useCallback, forwardRef } from 'react';
import { Copy, Check, FileText } from 'lucide-react';
import { cn } from '@/utils';

/**
 * MDX 代码块组件属性
 */
export interface MDXCodeBlockProps extends HTMLAttributes<HTMLPreElement> {
  /** 代码内容 */
  children?: React.ReactNode;
  /** 代码语言类名 */
  className?: string;
  /** 文件名 */
  filename?: string;
  /** 是否显示行号 */
  showLineNumbers?: boolean;
  /** 高亮的行号 */
  highlightLines?: number[];
  /** 是否启用复制功能 */
  copyable?: boolean;
}

/**
 * 代码语言映射和显示名称
 */
const LANGUAGE_MAP: Record<string, { name: string; icon?: string }> = {
  javascript: { name: 'JavaScript', icon: '🟨' },
  typescript: { name: 'TypeScript', icon: '🔷' },
  jsx: { name: 'React JSX', icon: '⚛️' },
  tsx: { name: 'React TSX', icon: '⚛️' },
  json: { name: 'JSO<PERSON>', icon: '📄' },
  html: { name: 'HTML', icon: '🌐' },
  css: { name: 'CSS', icon: '🎨' },
  scss: { name: 'SCSS', icon: '🎨' },
  markdown: { name: 'Markdown', icon: '📝' },
  mdx: { name: 'MDX', icon: '📝' },
  bash: { name: 'Bash', icon: '💻' },
  shell: { name: 'Shell', icon: '💻' },
  python: { name: 'Python', icon: '🐍' },
  java: { name: 'Java', icon: '☕' },
  go: { name: 'Go', icon: '🐹' },
  rust: { name: 'Rust', icon: '🦀' },
  php: { name: 'PHP', icon: '🐘' },
  sql: { name: 'SQL', icon: '🗄️' },
  yaml: { name: 'YAML', icon: '📋' },
  toml: { name: 'TOML', icon: '📋' },
  xml: { name: 'XML', icon: '📄' },
  dockerfile: { name: 'Dockerfile', icon: '🐳' },
  text: { name: 'Text', icon: '📄' },
};

/**
 * 提取代码内容的工具函数
 */
const extractCodeContent = (children: React.ReactNode): string => {
  if (typeof children === 'string') {
    return children;
  }

  if (React.isValidElement(children)) {
    const props = children.props as { children?: React.ReactNode };
    if (props.children) {
      return extractCodeContent(props.children);
    }
  }

  if (Array.isArray(children)) {
    return children.map(child => extractCodeContent(child)).join('');
  }

  return String(children || '');
};

/**
 * MDX 代码块渲染组件
 * - 支持 Shiki 语法高亮
 * - 支持代码复制
 * - 显示文件名和语言
 * - 响应式设计
 * - 主题适配
 */
export const MDXCodeBlock = forwardRef<HTMLPreElement, MDXCodeBlockProps>(
  (
    {
      children,
      className,
      filename,
      showLineNumbers = false,
      highlightLines = [],
      copyable = true,
      ...props
    },
    ref
  ) => {
    // 提取语言信息
    const language = className?.replace(/language-/, '') || 'text';
    const languageInfo = LANGUAGE_MAP[language] || { name: language, icon: '📄' };

    // 提取代码内容
    const codeContent = extractCodeContent(children);

    // 复制状态
    const [isCopied, setIsCopied] = useState(false);

    // 复制代码到剪贴板
    const copyToClipboard = useCallback(async () => {
      try {
        await navigator.clipboard.writeText(codeContent);
        setIsCopied(true);
        setTimeout(() => setIsCopied(false), 2000);
      } catch (err) {
        console.error('Failed to copy code:', err);
      }
    }, [codeContent]);

    return (
      <div
        className={cn(
          // 外层容器样式
          'group relative my-6 overflow-hidden',
          'rounded-lg border border-border',
          'bg-card shadow-sm',
          // 代码块特定样式
          'not-prose',
          className
        )}
      >
        {/* 头部：文件名和操作按钮 */}
        <div
          className={cn(
            'flex items-center justify-between',
            'border-b border-border px-4 py-3',
            'bg-muted/30 dark:bg-muted/20'
          )}
        >
          <div className="flex items-center gap-2">
            {languageInfo.icon && (
              <span className="text-sm" role="img" aria-label={languageInfo.name}>
                {languageInfo.icon}
              </span>
            )}
            <span className="text-sm font-medium text-foreground">
              {filename || languageInfo.name}
            </span>
            {filename && (
              <span className="text-xs text-muted-foreground">({languageInfo.name})</span>
            )}
          </div>

          {copyable && (
            <button
              type="button"
              onClick={copyToClipboard}
              className={cn(
                'flex items-center gap-1.5 rounded-md px-2.5 py-1.5',
                'text-xs font-medium transition-all duration-200',
                'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
                isCopied
                  ? 'bg-green-500/10 text-green-600 dark:text-green-400'
                  : 'bg-muted hover:bg-muted/80 text-muted-foreground hover:text-foreground'
              )}
              aria-label={isCopied ? '已复制到剪贴板' : '复制代码'}
            >
              {isCopied ? (
                <>
                  <Check className="h-3.5 w-3.5" />
                  <span>已复制</span>
                </>
              ) : (
                <>
                  <Copy className="h-3.5 w-3.5" />
                  <span>复制</span>
                </>
              )}
            </button>
          )}
        </div>

        {/* 代码内容区域 */}
        <div className="relative">
          <pre
            ref={ref}
            className={cn(
              // 基础样式
              'w-full overflow-x-auto',
              'p-4 m-0',
              'text-sm font-mono leading-relaxed',
              'bg-muted/20 dark:bg-muted/10',
              // 移除默认样式
              'border-0 outline-0',
              // 确保代码内容可选择
              'select-text',
              // Shiki 样式兼容
              '[&>code]:block [&>code]:w-full',
              '[&>code]:font-inherit [&>code]:text-inherit',
              '[&>code]:bg-transparent [&>code]:p-0 [&>code]:m-0',
              // 行号支持
              showLineNumbers && [
                '[&>code]:counter-reset-[line]',
                '[&_.line]:before:counter-increment-[line]',
                '[&_.line]:before:content-[counter(line)]',
                '[&_.line]:before:inline-block [&_.line]:before:w-8',
                '[&_.line]:before:text-right [&_.line]:before:mr-4',
                '[&_.line]:before:text-muted-foreground/60',
                '[&_.line]:before:select-none',
              ],
              // 滚动条样式
              'scrollbar-thin scrollbar-track-transparent',
              'scrollbar-thumb-border/30 hover:scrollbar-thumb-border/50'
            )}
            {...props}
          >
            <code
              className={cn(
                `language-${language}`,
                'block w-full font-inherit',
                'whitespace-pre text-inherit',
                'bg-transparent border-0 p-0 m-0'
              )}
              data-language={language}
            >
              {children}
            </code>
          </pre>
        </div>
      </div>
    );
  }
);

MDXCodeBlock.displayName = 'MDXCodeBlock';
