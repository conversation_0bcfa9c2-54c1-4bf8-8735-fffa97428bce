---
title: VitePress 文档站点开发指南
description: 详细讲解基于 Vue 和 Vite 的 VitePress 文档建站流程，覆盖项目配置、主题定制、Markdown 增强、国际化等进阶特性
date: "2024-09-24"
category: "前端开发"
tags:
- VitePress
- Vue3
- Vite
- Markdown
- 主题定制
- 文档站点
---

> VitePress 是一个静态站点生成器（SSG），由 Vue 之父尤雨溪所开发，深度集成了 Vite、Vue 和 Markdown，非常适合搭建文档类站点。

## 网站部署

### 前置条件

- 安装 [Node.js](https://nodejs.org/) ，建议18 及以上版本。

- 安装支持 Markdown 语法的编辑器（如 [VSCode](https://code.visualstudio.com/) ）及[官方 Vue 扩展](https://marketplace.visualstudio.com/items?itemName=Vue.volar)。

### 安装步骤

#### 安装依赖

在命令行工具中，输入以下任一命令：

npm

```sh
npm add -D vitepress
```

pnpm

```sh
pnpm add -D vitepress
```

yarn

```sh
yarn add -D vitepress
```

#### 启动向导

运行以下任一命令

npm

```sh
npx vitepress init
```

pnpm

```sh
pnpm vitepress init
```

yarn

```sh
yarn vitepress init
```

### 文件结构

如在默认的 `./docs` 中安装，则生成的文件结构如下：

```text
.
├─ docs
│  ├─ .vitepress
│  │  └─ config.mts
│  ├─ api-examples.md
│  ├─ markdown-examples.md
│  └─ index.md
└─ package.json
```

`docs`  是 VitePress 的根目录；`.vitepress` 是配置文件 (`.vitepress/config.mts`) 、开发服务器缓存（ `.vitepress/cache` ）、构建输出（ `.vitepress/dist` ）的目录；以后对网址界面进行深度定制，也在这个目录下。

`.vitepress` 目录之外的 `.md` 文件即为源文件，将会被编译为 `.html` 文件。其中，`index.md` 将被编译为 `index.html`，作为网址的主页。

### 本地开发

在根目录下输入以下任一命令，启动本地开发服务器：

npm

```sh
npm run docs:dev
```

pnpm

```sh
pnpm run docs:dev
```

yarn

```sh
yarn docs:dev
```

也可以直接调用 VitePress：

npm

```sh
npx vitepress dev docs
```

pnpm

```sh
pnpm vitepress dev docs
```

yarn

```sh
yarn vitepress dev docs
```

启动后，在浏览器中访问 `http://localhost:5173` 即可查看效果，本地的所有改动，都可以及时预览。

### 远程部署

建议将项目上传到 GitHub 上（如涉及个人隐私，可设为私有仓库），再导入 Vercel 中。

#### 确认安装目录

在此之前，需要确认：

- VitePress 站点位于 `docs` 目录下。

- 默认生成目录在（`.vitepress/dist`）中。

#### 生成静态文件

运行以下命令，生成网站静态文件：

```sh
npm run docs:build
```

继续运行以下命令，可在本地预览：

```sh
npm run docs:preview
```

用浏览器打开 `http://localhost:4173`，进行预览和检查。

#### 上传到 GitHub

1. 在 GitHub 新建一个仓库，并获取仓库地址。

2. 在根目录下输入以下命令，对 Git 进行初始化：

```sh
git init
```

3. 然后输入以下命令，暂存所有需要提交的信息：

```sh
git add .
```

4. 接着输入以下命令，撰写更新说明（将 `说明` 更改为你自己的内容）：

```sh
git commit -m "说明"
```

5. 选择想要上传的分支，默认主分支，则输入以下命令：

```sh
git branch -M main
```

6. 继续输入以下命令，将内容上传至你的 GitHub 仓库（仅用于与本地目录关联，之后更新可省略这一步）：

```sh
git remote <NAME_EMAIL> : 用户名/仓库名. git
```

7. 最后输入以下命令，完成上传：

```sh
git push -u origin main
```

#### 部署到 Vercel

新建项目，并更改如下设置，不然会报错。

填写构建命令：

```sh
npm run docs:build
```

选择输出目录：

```sh
docs/.vitepress/dist
```

部署完成后，可继续绑定自己的域名。

## 内容创作

### Markdown 扩展

VitePress 内置 Markdown 扩展，更多用法参考 [Markdown 扩展](https://vitepress.dev/zh/guide/markdown)，以下仅列举本人常用的功能。

#### Emoji

基本用法为“冒号+Emoji 名称+冒号”，例如输入 `:tada:` ，生成🎉。

目前大部分支持 Markdown 的编辑器，支持直接复制粘贴 Emoji，只需在[所有支持的 emoji 列表](https://github.com/markdown-it/markdown-it-emoji/blob/master/lib/data/full.mjs) 中选择自己喜欢的，复制即可。

#### 文档目录

只需输入 `[[toc]]` ，即可生成。

#### 代码高亮

参考 [Markdown 扩展 - 代码块中的语法高亮](https://vitepress.dev/zh/guide/markdown#syntax-highlighting-in-code-blocks)

### frontmatter

VitePress 支持在所有 Markdown 文件中使用 YAML frontmatter，并使用 [gray-matter](https://github.com/jonschlinkert/gray-matter) 解析。

frontmatter 必须位于 Markdown 文件的顶部 (在任何元素之前，包括 `<script>` 标签)，并且需要在三条虚线之间采用有效的 YAML 格式。例如：

```text
---
title: Docs with VitePress
editLink: true
---
```

更多用法参考 [frontmatter 配置](https://vitepress.dev/zh/reference/frontmatter-config)。