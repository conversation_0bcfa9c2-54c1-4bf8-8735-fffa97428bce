'use client';

import React, { useState, forwardRef } from 'react';
import { cn } from '@/utils';
import { MDXCodeBlock } from '@/components/mdx/MDXCodeBlock';

interface CodeTab {
  label: string;
  language: string;
  code: string;
  filename?: string;
}

interface MDXCodeGroupProps {
  tabs: CodeTab[];
  defaultTab?: number;
  className?: string;
}

/**
 * MDX 代码组组件
 * - 支持多个代码块切换
 * - 保持选中状态
 * - 自动语法高亮
 * - 统一的设计风格
 */
export const MDXCodeGroup = forwardRef<HTMLDivElement, MDXCodeGroupProps>(
  ({ tabs, defaultTab = 0, className }, ref) => {
    const [activeTab, setActiveTab] = useState(defaultTab);

    if (!tabs || tabs.length === 0) {
      return null;
    }

    return (
      <div
        ref={ref}
        className={cn(
          'my-6 overflow-hidden rounded-lg',
          'border border-border bg-card',
          'shadow-sm',
          className
        )}
      >
        {/* 标签栏 */}
        <div
          className={cn(
            'flex overflow-x-auto',
            'bg-muted/30 border-b border-border',
            'scrollbar-thin scrollbar-track-transparent scrollbar-thumb-border/50'
          )}
        >
          {tabs.map((tab, index) => (
            <button
              key={index}
              type="button"
              onClick={() => setActiveTab(index)}
              className={cn(
                'px-4 py-3 text-sm font-medium whitespace-nowrap',
                'border-b-2 transition-all duration-200',
                'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
                activeTab === index
                  ? 'text-primary border-primary bg-background/50'
                  : 'text-muted-foreground border-transparent hover:text-foreground hover:bg-muted/50'
              )}
              aria-selected={activeTab === index}
              role="tab"
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* 代码内容 */}
        <div className="relative" role="tabpanel">
          {tabs.map((tab, index) => (
            <div
              key={index}
              className={cn(
                'transition-opacity duration-200',
                activeTab === index ? 'block' : 'hidden'
              )}
              aria-hidden={activeTab !== index}
            >
              <MDXCodeBlock
                className={`language-${tab.language}`}
                filename={tab.filename}
                copyable={true}
              >
                {tab.code}
              </MDXCodeBlock>
            </div>
          ))}
        </div>
      </div>
    );
  }
);

MDXCodeGroup.displayName = 'MDXCodeGroup';
