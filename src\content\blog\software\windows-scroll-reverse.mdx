---
title: Windows 反转滚轮方向
description: 通过修改注册表实现 Windows 系统鼠标滚轮方向的反转，使其与触屏设备操作逻辑一致
date: 2025-01-12
category: 软件
tags:
- Windows
- 鼠标设置
- 注册表修改
---

习惯了触屏设备的操作逻辑，总感觉 Windows 鼠标的滚轮方向是反的，下面就来改一下！

## 一、查找鼠标属性

使用Windows + R快捷键打开「运行」对话框，执行 devmgmt.msc 打开设备管理器。

![打开设备管理器](https://img.dava.cc/img/打开设备管理器.png)

在「设备管理器」的「鼠标和其它指针设备」中，右键点击要更改的鼠标，然后选择「属性」。

![设备管理器属性](https://img.dava.cc/img/设备管理器属性.png)

在弹出的「属性」窗口中选择「详细信息」选项卡，在「属性」下拉列表中选择「设备实例路径」，然后记录下显示的 VID 和 PID 信息，这将在后续的注册表编辑中用到。

![设备实例路径](https://img.dava.cc/img/设备实例路径.png)

## 二、修改注册表

使用Windows + R快捷键打开「运行」对话框，执行regedit打开注册表编辑器。

![注册表编辑器](https://img.dava.cc/img/注册表编辑器.png)

导航到以下目录：

```text
HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Enum\HID
```

展开与鼠标 VID 和 PID 值相匹配键（文件夹），选择 Device Parameters，然后双击名为FlipFlopWheel 的 DWORD（32 位）值。

将其值设置为"1"，确定后重启系统即可。

![FlipFlopWheel](https://img.dava.cc/img/FlipFlopWheel.png)

> 说明：
> 0 反向滚动（默认）
> 1 自然滚动（更改后）