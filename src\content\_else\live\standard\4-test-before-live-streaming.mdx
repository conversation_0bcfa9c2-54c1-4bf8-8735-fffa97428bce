---
title: 播前测试内容
description: 直播开播前的硬件、软件、网络等技术测试指南
date: 2024-01-05
category: live
tags:
  - 技术测试
  - 设备检查
  - 直播准备
---

开播前进行以下测试，避免产生未知影响。

## 一、硬件检查

满足以下两项之一即可。

### 手机直播

- ios：建议iPhoneX以上
- Android：建议使用品牌旗舰机

### 电脑直播

- 系统：建议Windows 10以上
- CPU：建议intel I7
- 内存：建议8G以上，测试不卡即可

## 二、软件检查

- 手机端：通过抖音APP开播（设置私密直播间，进行小范围测试）
- 电脑端：使用[直播伴侣](https://streamingtool.douyin.com/docs/guide/live/)开播（仅限Windows系统，抖音千粉以上账号）

## 三、网络测速

通过[测速网](https://www.speedtest.cn/)测试网络速度。

> 同一网络状况下保持仅有主播设备连接，不要有其他设备抢占网速。
> 上传带宽要求≥10Mbps，如果有网络主备方案，主备网络上行均需要测试。

## 四、开播测试

### 操作步骤

1. 在正式网络环境下，使用正式开播设备登录低粉账号，并将账号设置为私密。
2. 账号开播测试，用其他账号进直播间观众端，查看直播效果。

### 测试项目

- 测试账号开播，观众端观察是否可以正常看到直播。
- 测试主播活动中会用到的直播间功能，观察直播基础功能是否正常。
- 测试主播露出人像，观察直播清晰度（关注环境光线和主播画质）是否满足活动需求。
- 测试主播说话、放音乐，观察直播声音（关注音量、音质）是否正常。
- 主备网络（如果有）切换1次，观察直播间是否有卡顿线上，容灾效果是否可接受。
- 保持当前直播测试状态10分钟（含前序步骤耗时），观察直播是否有卡顿现象。

如无其他测试项，可以下播，如有连麦，继续下一步。

### 连麦测试

用另外的账号与主播连麦，继续观察。

- 主播连线是否可以正常连接，无黑屏、无流、连麦失败等问题。
- 连麦双方下麦一次，再重新连线，观察是否有连麦上下麦卡顿。
- 双方测试主播露出人像，观察连麦清晰度是否满足活动需求。
- 双方测试主播对话，观察直播声音是否正常（关注音量、回声）。
- 主备网络（如果有）切换1次，观察直播间是否有卡顿线上，容灾效果是否可接受。
- 保持当前连麦测试状态10分钟（含前序步骤耗时），观察连麦是否有卡顿现象。

如无其他测试项，可以下播。